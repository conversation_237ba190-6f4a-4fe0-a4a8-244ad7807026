package org.springblade.miniapp.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springblade.business.group.entity.GroupCategory;
import org.springblade.business.group.entity.GroupInfo;
import org.springblade.business.group.mapper.GroupCategoryMapper;
import org.springblade.business.group.mapper.GroupInfoMapper;
import org.springblade.business.group.vo.GroupCategoryVO;
import org.springblade.business.group.vo.GroupInfoVO;
import org.springblade.miniapp.service.WechatGroupService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @email <EMAIL>
 * @website duofan.top
 * @date 2025/7/26
 */
@Slf4j
@Service
public class WechatGroupServiceImpl implements WechatGroupService {


    @Resource
    private  GroupCategoryMapper groupCategoryMapper;
    @Resource
    private  GroupInfoMapper groupInfoMapper;

    @Override
    public IPage<GroupCategoryVO> selectGroupCategoryPage(IPage<GroupCategoryVO> page, GroupCategoryVO groupCategory) {
       return page.setRecords(groupCategoryMapper.selectGroupCategoryPage(page,groupCategory));
    }

    @Override
    public IPage<GroupInfoVO> selectGroupInfoPage(IPage<GroupInfoVO> page, GroupInfoVO groupInfo) {
        return page.setRecords(groupInfoMapper.selectGroupInfoPage(page,groupInfo));
    }

}
