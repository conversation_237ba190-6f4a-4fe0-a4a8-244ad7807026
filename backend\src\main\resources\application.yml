#服务器配置
server:
  port: 80
  undertow:
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
    buffer-size: 1024
    # 是否分配的直接内存
    direct-buffers: true
    # 线程配置
    threads:
      # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
      io: 16
      # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载
      worker: 400
  servlet:
    # 编码配置
    encoding:
      charset: UTF-8
      force: true

#spring配置
spring:
  cache:
    type: redis
    redis:
      time-to-live: 3600000 # 1小时
      cache-null-values: false
  servlet:
    multipart:
      max-file-size: 256MB
      max-request-size: 1024MB
  web:
    resources:
      add-mappings: false
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver



#配置日志地址
logging:
  config: classpath:log/logback_${blade.env}.xml

# mybatis
mybatis-plus:
  mapper-locations: classpath:org/springblade/**/mapper/*Mapper.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: org.springblade.**.entity
  #typeEnumsPackage: org.springblade.dashboard.entity.enums
  global-config:
    # 关闭MP3.0自带的banner
    banner: false
    db-config:
      #主键类型  0:"数据库ID自增", 1:"不操作", 2:"用户输入ID",3:"数字型snowflake", 4:"全局唯一ID UUID", 5:"字符串型snowflake";
      id-type: assign_id
      #字段策略
      insert-strategy: not_null
      update-strategy: not_null
      where-strategy: not_null
      #驼峰下划线转换
      table-underline: true
      # 逻辑删除配置
      # 逻辑删除全局值（1表示已删除，这也是Mybatis Plus的默认配置）
      logic-delete-value: 1
      # 逻辑未删除全局值（0表示未删除，这也是Mybatis Plus的默认配置）
      logic-not-delete-value: 0
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#报表配置
report:
  enabled: false
  database:
    provider:
      prefix: blade-

#springdoc-openapi配置
springdoc:
  default-flat-param-object: true

#knife4j配置
knife4j:
  #启用
  enable: true
  #基础认证
  basic:
    enable: false
    username: blade
    password: blade
  #增强配置
  setting:
    enableSwaggerModels: true
    enableDocumentManage: true
    enableHost: false
    enableHostText: http://localhost
    enableRequestCache: true
    enableFilterMultipartApis: false
    enableFilterMultipartApiMethodType: POST
    language: zh_cn
    enableFooter: false
    enableFooterCustom: true
    footerCustomContent: Copyright © 2024 SpringBlade All Rights Reserved

#swagger配置信息
swagger:
  title: SpringBlade 接口文档系统
  description: SpringBlade 接口文档系统
  version: 4.4.0
  license: Powered By SpringBlade
  licenseUrl: https://bladex.cn
  terms-of-service-url: https://bladex.cn
  contact:
    name: smallchill
    email: <EMAIL>
    url: https://gitee.com/smallc

#oss配置
oss:
  enabled: true
  name: qiniu
  tenant-mode: true
  endpoint: http://prt1thnw3.bkt.clouddn.com
  access-key: N_Loh1ngBqcJovwiAJqR91Ifj2vgOWHOf8AwBA_h
  secret-key: AuzuA1KHAbkIndCU0dB3Zfii2O3crHNODDmpxHRS
  bucket-name: blade

#第三方登陆配置

#第三方登陆配置
social:
  oauth:
    GITHUB:
      client-id: 233************
      client-secret: 233************************************
      redirect-uri: ${social.domain}/oauth/redirect/github
    GITEE:
      client-id: 233************
      client-secret: 233************************************
      redirect-uri: ${social.domain}/oauth/redirect/gitee
    WECHAT_OPEN:
      ignore-check-state: true
      client-id: wx5f0591468a438c48
      client-secret: aa1f75842cd317a05f52d40f73f1259e
      ignore-check-redirect-uri: true
    QQ:
      client-id: 233************
      client-secret: 233************************************
      redirect-uri: ${social.domain}/oauth/redirect/qq
    DINGTALK:
      client-id: 233************
      client-secret: 233************************************
      redirect-uri: ${social.domain}/oauth/redirect/dingtalk
wx:
  miniapp:
    configs:
      - appid: wx5f0591468a438c48
        secret: bbdbb042eb8a9d55f032f53a41b99c82
        msgDataFormat: JSON
        #        token: #微信小程序消息服务器配置的token
        #        aesKey: #微信小程序消息服务器配置的EncodingAESKey
    main-app-id: wx5f0591468a438c48
    main-secret: bbdbb042eb8a9d55f032f53a41b99c82
wepay:
  tenant-configs:
    674743: # 租户ID
      mch-id: ${WEPAY_MCH_ID_TENANT1:1704897571}
      # 微信商户密钥
      mch-key: ${WEPAY_MCH_KEY_TENANT1:AkNOMREwDwYDVQQHDAhTaGVuWmhlbjCC}
      app-id: ${WEPAY_APP_ID_TENANT1:wxc863a42f8df45139}
      api-key: ${WEPAY_API_KEY_TENANT1:a6f238ed8a5504f1a972c84c5c6c9540}
      notify-url: ${WEPAY_NOTIFY_URL_TENANT1:https://312264ue4jd1.vicp.fun/pay-service/payment/wepay/callback/}


#blade配置
blade:
  auth:
    #使用 @org.springblade.test.Sm2KeyGenerator 获取,用于国密sm2验签,需和前端保持一致
    public-key: 04cce367a10638f96e0ee44a0bd75d9a9e2fb520a95553e4d553ece4f597e0e5fc2cbec51df58625acf016379c7ac1487aaccf046bb111e929589e0f8448d398e1
    #使用 @org.springblade.test.Sm2KeyGenerator 获取,用于国密sm2解密,前端无需配置
    private-key: 625a0ddda71b4179a7677931133abfa728cce4cafcc8159fc961636b640f6a1d
  token:
    #使用 @org.springblade.test.SignKeyGenerator 获取
    sign-key: 4ll4SvFBH5VKN6KZEnSxQBc7GxXSuz9N
    #使用 @org.springblade.test.SignKeyGenerator 获取
    aes-key: E9B837hj2Aa9td7XMrTHrxY5ZM8WHJde
  xss:
    enabled: true
    skip-url:
      - /blade-test/**
      - /blade-ad/notice/**
  secure:
    skip-url:
      - /blade-test/**
    client:
      - client-id: sword
        path-patterns:
          - /blade-sword/**
      - client-id: saber
        path-patterns:
          - /blade-saber/**
  tenant:
    column: tenant_id
    tables:
      - blade_notice

  storage:
    # 存储提供商：aliyun-oss, cloudflare
    provider: aliyun-oss

    # 阿里云OSS配置
    aliyun-oss:
      access-key-id: LTAI5tSfX9tTMf79fvC26oX3
      access-key-secret: ******************************
      endpoint: oss-cn-hangzhou.aliyuncs.com
      bucket-name: gerenuk
      domain: https://gerenuk.oss-cn-hangzhou.aliyuncs.com   # 可选，自定义域名
      region: cn-hangzhou

    # Cloudflare配置
    cloudflare:
      account-id: your-account-id
      api-token: your-api-token
      bucket-name: your-bucket-name
      domain: https://your-domain.com

    # 文件上传配置
    upload:
      # 最大文件大小（MB）
      max-file-size: 10

      # 允许的文件类型
      allowed-types:
        - image/jpeg
        - image/png
        - image/gif
        - image/webp
        - application/pdf
        - application/msword
        - application/vnd.openxmlformats-officedocument.wordprocessingml.document
        - text/plain
        - text/csv
        - video/mp4
        - video/avi
        - audio/mpeg
        - audio/wav

      # 存储路径前缀
      path-prefix: uploads

      # 是否生成缩略图
      generate-thumbnail: true

      # 缩略图尺寸
      thumbnail-width: 200
      thumbnail-height: 200


# 文件上传大小限制
