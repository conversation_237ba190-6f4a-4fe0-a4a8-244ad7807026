/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.miniapp.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.miniapp.service.WeChatRegionService;
import org.springblade.miniapp.vo.MiniappRegionVO;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 小程序地区选择控制器
 *
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/miniapp/region")
@Tag(name = "小程序地区选择", description = "小程序地区选择接口")
public class WeChatRegionController extends BladeController {

	private final WeChatRegionService miniappRegionService;

	/**
	 * 获取开放的城市列表（直辖市和地级市）
	 */
	@GetMapping("/cities")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "获取开放的城市列表", description = "获取小程序开放的城市列表")
	public R<List<MiniappRegionVO>> getCities() {
		List<MiniappRegionVO> cities = miniappRegionService.getOpenRegionsByLevel(2);
		return R.data(cities);
	}

	/**
	 * 根据城市编码获取开放的区县列表
	 */
	@GetMapping("/districts")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "获取开放的区县列表", description = "根据城市编码获取开放的区县列表")
	public R<List<MiniappRegionVO>> getDistricts(@Parameter(description = "城市编码", required = true) @RequestParam String cityCode) {
		List<MiniappRegionVO> districts = miniappRegionService.getOpenRegionsByParentCode(cityCode, 3);
		return R.data(districts);
	}



	/**
	 * 根据地区编码获取完整地址信息
	 */
	@GetMapping("/full-address")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "获取完整地址信息", description = "根据地区编码获取完整的市县地址信息")
	public R<MiniappRegionVO> getFullAddress(@Parameter(description = "地区编码", required = true) @RequestParam String regionCode) {
		MiniappRegionVO fullAddress = miniappRegionService.getFullAddressByCode(regionCode);
		return R.data(fullAddress);
	}

	/**
	 * 搜索地区（仅搜索市和区县）
	 */
	@GetMapping("/search")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "搜索地区", description = "根据关键词搜索开放的城市和区县")
	public R<List<MiniappRegionVO>> searchRegions(@Parameter(description = "搜索关键词", required = true) @RequestParam String keyword) {
		List<MiniappRegionVO> regions = miniappRegionService.searchOpenRegions(keyword);
		return R.data(regions);
	}

}
