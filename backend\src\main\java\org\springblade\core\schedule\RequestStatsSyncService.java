package org.springblade.core.schedule;

import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springblade.business.statistics.entity.UserRequestStats;
import org.springblade.business.user.mapper.UserLoginStatsMapper;
import org.springblade.business.user.service.RedisRequestStatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.time.LocalDate;
import java.util.concurrent.TimeUnit;

@Service
public class RequestStatsSyncService{
    private static final Logger logger = LoggerFactory.getLogger(RequestStatsSyncService.class);
    @Autowired
    private RedisRequestStatsService redisService;

    @Autowired
    private UserLoginStatsMapper statsMapper;

    /**
     *  每天凌晨4点触发
     */
	@Scheduled(cron = "0 0 4 * * ?")
    public void syncRequestStatsToMySQL() {
        logger.info("开始执行Redis请求数据同步到MySQL任务...");
        try {
            // 1. 获取今日所有活跃用户ID
            Set<Object> userIds = redisService.getActiveUserList(LocalDate.now().minusDays(1).format(DateTimeFormatter.BASIC_ISO_DATE));
            if (userIds.isEmpty()) {
                logger.info("今日无活跃用户，同步任务结束");
                return;
            }

            // 2. 批量转换为数据库实体
            List<UserRequestStats> statsList = new ArrayList<>();
			//获取同步日期
            LocalDate today = LocalDate.now().minusDays(1);
            Date syncDate = Date.from(today.atStartOfDay(ZoneId.systemDefault()).toInstant());

            for (Object userId : userIds) {
                UserRequestStats stats = new UserRequestStats();
                stats.setUserId(Long.valueOf(String.valueOf(userId)));
				Long lastRequestTime =redisService.getUserLastRequestTime(String.valueOf(userId));
				if(lastRequestTime!=null){
					stats.setLastRequestTime(new Date(lastRequestTime));
				}
				String userRequestCount = redisService.getUserRequestCount(String.valueOf(userId));
				if(userRequestCount!=null){
					stats.setTodayRequestCount(Integer.parseInt(userRequestCount));
				}
				stats.setSyncTime(syncDate);
                statsList.add(stats);
            }

            // 3. 批量写入MySQL（使用INSERT ON DUPLICATE KEY UPDATE）
            int count = statsMapper.batchUpsert(statsList);
            logger.info("Redis登录数据同步完成，共同步 {} 条记录", count);


        } catch (Exception e) {
            logger.error("Redis登录数据同步失败", e);
            // 可添加重试逻辑或告警机制
        }
    }
}
