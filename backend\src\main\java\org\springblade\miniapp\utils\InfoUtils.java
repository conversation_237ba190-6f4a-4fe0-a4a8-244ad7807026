package org.springblade.miniapp.utils;

import org.springblade.common.constant.bizz.AuditStatusEnum;
import org.springblade.common.constant.bizz.PublishStatusEnum;

/**
 * <AUTHOR>
 * @version 1.0
 * @email <EMAIL>
 * @website duofan.top
 * @date 2025/8/1
 */
public class InfoUtils {

	/**
	 * 用户信息公示逻辑
	 *
	 * @param paramsVal
	 * @param auditStatus
	 * @param publishStatus
	 * @return
	 */
	public static boolean userInfoPublishLogic(String paramsVal, String auditStatus, String publishStatus) {
		if ("false".equals(paramsVal) && PublishStatusEnum.PUBLISHED.getValue().equals(publishStatus)) {
			return true;
		}
        return "true".equals(paramsVal) && PublishStatusEnum.PUBLISHED.getValue().equals(publishStatus)
                && auditStatus.equals(AuditStatusEnum.APPROVED.getCode());
    }


}
