package org.springblade.miniapp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springblade.business.post.entity.Category;
import org.springblade.business.post.entity.Tag;
import org.springblade.business.post.service.ITagService;
import org.springblade.business.user.mapper.CategoryMapper;
import org.springblade.business.post.vo.CategoryVO;
import org.springblade.business.user.mapper.SupPostMapper;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.miniapp.service.WeChatCategoryService;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static org.springblade.common.cache.CacheNames.WECHAT_CATEGORY_LIST;

@Slf4j
@Service
public class WeChatCategoryServiceImpl implements WeChatCategoryService {
	@Resource
	private CategoryMapper categoryMapper;

	@Resource
	private ITagService tagService;

	@Resource
	private RedisTemplate<String, Object> redisTemplate;

	@Resource
	private SupPostMapper subPostMapper;

	@Override
	public IPage<CategoryVO> getCategoryList(Query query) {
		// 手动缓存管理，避免缓存复杂的IPage对象
		String cacheKey = WECHAT_CATEGORY_LIST;

		// 尝试从缓存获取
		Map<String, Object> cachedData = (Map<String, Object>) redisTemplate.opsForValue().get(cacheKey);
		if (cachedData != null) {
			// 从缓存恢复IPage对象
			IPage<CategoryVO> page = Condition.getPage(query);
			page.setRecords((List<CategoryVO>) cachedData.get("records"));
			page.setTotal((Long) cachedData.get("total"));
			page.setSize((Long) cachedData.get("size"));
			page.setCurrent((Long) cachedData.get("current"));
			page.setPages((Long) cachedData.get("pages"));
			return page;
		}

		// 缓存未命中，从数据库查询
		IPage<CategoryVO> page = categoryMapper.selectCategoryList(Condition.getPage(query), null);

		// 将IPage转换为可序列化的Map结构来缓存
		Map<String, Object> cacheData = new HashMap<>();
		cacheData.put("records", page.getRecords());
		cacheData.put("total", page.getTotal());
		cacheData.put("size", page.getSize());
		cacheData.put("current", page.getCurrent());
		cacheData.put("pages", page.getPages());

		// 手动缓存，设置30分钟过期
		redisTemplate.opsForValue().set(cacheKey, cacheData, 30, TimeUnit.MINUTES);
		return page;
	}


	@Override
	@Cacheable(cacheNames = WECHAT_CATEGORY_LIST)
	public List<Category> getEnabledCategories() {
		LambdaQueryWrapper<Category> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(Category::getEnabled, 1)
			.orderByAsc(Category::getSort)
			.orderByAsc(Category::getId);

		List<Category> categories = categoryMapper.selectList(queryWrapper);

		// 加载每个分类的标签
		for (Category category : categories) {
			List<Tag> tags = tagService.getTagsByCategory(category.getId());
			//根据分类id查询帖子总数
			category.setPostCount(subPostMapper.getPostCountByCategoryId(category.getId()));
			category.setTags(tags);
		}

		return categories;
	}

	/**
	 * 清理分类相关缓存
	 */
	public void clearCategoryCache() {
		try {
			// 清理微信分类相关的所有缓存
			Set<String> keys = redisTemplate.keys("wechat:category:*");
			if (keys != null && !keys.isEmpty()) {
				redisTemplate.delete(keys);
				log.info("清理分类缓存成功，删除数量: {}", keys.size());
			}
		} catch (Exception e) {
			log.error("清理分类缓存时发生错误", e);
		}
	}
}
