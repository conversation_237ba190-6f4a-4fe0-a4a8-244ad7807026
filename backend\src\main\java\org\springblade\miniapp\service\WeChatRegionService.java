/**
 * Copyright (c) 2018-2099, Chill <PERSON>ang 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.miniapp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springblade.business.config.entity.MiniappRegionConfig;
import org.springblade.miniapp.vo.MiniappRegionVO;
import org.springframework.cache.annotation.Caching;

import java.util.List;

/**
 * 小程序地区选择服务接口
 *
 * <AUTHOR>
 */
public interface WeChatRegionService extends IService<MiniappRegionConfig> {

	/**
	 * 根据层级获取开放的地区列表
	 *
	 * @param level 地区层级
	 * @return 地区列表
	 */
	List<MiniappRegionVO> getOpenRegionsByLevel(Integer level);

	/**
	 * 根据父级编码和层级获取开放的地区列表
	 *
	 * @param parentCode 父级编码
	 * @param level      地区层级
	 * @return 地区列表
	 */
	List<MiniappRegionVO> getOpenRegionsByParentCode(String parentCode, Integer level);

	/**
	 * 根据地区编码获取完整地址信息
	 *
	 * @param regionCode 地区编码
	 * @return 完整地址信息
	 */
	MiniappRegionVO getFullAddressByCode(String regionCode);

	/**
	 * 搜索开放的地区
	 *
	 * @param keyword 搜索关键词
	 * @return 地区列表
	 */
	List<MiniappRegionVO> searchOpenRegions(String keyword);

	/**
	 * 根据经纬度获取对应的地区信息
	 *
	 * @param latitude  纬度
	 * @param longitude 经度
	 * @return 地区信息
	 */
	MiniappRegionVO getRegionByLocation(Double latitude, Double longitude);

	/**
	 * 配置地区开放状态
	 *
	 * @param regionCode 地区编码
	 * @param isOpen     是否开放
	 * @return 是否成功
	 */
	boolean configRegionOpen(String regionCode, Integer isOpen);

	/**
	 * 批量配置地区开放状态
	 *
	 * @param regionCodes 地区编码列表
	 * @param isOpen      是否开放
	 * @return 是否成功
	 */
	boolean batchConfigRegionOpen(List<String> regionCodes, Integer isOpen);

	/**
	 * 同步地区数据到小程序配置表
	 *
	 * @return 是否成功
	 */
	boolean syncRegionData();

}
