package org.springblade.miniapp.service.impl;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.WebUtil;
import org.springblade.miniapp.mapper.DataOperateMapper;
import org.springblade.miniapp.service.WeChatDataOperateService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 数据操作服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @email <EMAIL>
 * @website duofan.top
 * @date 2025/7/31
 */
@Slf4j
@Service
public class WeChatDataOperateServiceImpl implements WeChatDataOperateService {

	@Resource
	private DataOperateMapper mapper;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean toggleLike(Long id, String type) {
		Long userId = AuthUtil.getUserId();

		// 检查用户是否已经点赞
		int exists = mapper.checkLikeExists(id, type, userId);

		if (exists > 0) {
			// 已点赞，取消点赞
			int result = mapper.deleteLike(id, type, userId);
			log.debug("用户 {} 取消点赞 {} ID: {}, 结果: {}", userId, type, id, result > 0);
			return false; // 返回false表示已取消点赞
		} else {
			// 未点赞，添加点赞
			int result = mapper.insertLike(id, type, userId);
			log.debug("用户 {} 点赞 {} ID: {}, 结果: {}", userId, type, id, result > 0);
			return true; // 返回true表示已点赞
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean toggleFavorite(Long id, String type) {
		Long userId = AuthUtil.getUserId();

		// 检查用户是否已经收藏
		int exists = mapper.checkFavoriteExists(id, type, userId);

		if (exists > 0) {
			// 已收藏，取消收藏
			int result = mapper.deleteFavorite(id, type, userId);
			log.debug("用户 {} 取消收藏 {} ID: {}, 结果: {}", userId, type, id, result > 0);
			return false; // 返回false表示已取消收藏
		} else {
			// 未收藏，添加收藏
			int result = mapper.insertFavorite(id, type, userId);
			log.debug("用户 {} 收藏 {} ID: {}, 结果: {}", userId, type, id, result > 0);
			return true; // 返回true表示已收藏
		}
	}

	@Override
	public boolean share(Long id, String type) {
		Long userId = AuthUtil.getUserId();
		String ip = WebUtil.getIP();

		int result = mapper.insertShare(id, type, userId, ip);
		log.debug("用户 {} 分享 {} ID: {}, IP: {}, 结果: {}", userId, type, id, ip, result > 0);
		return result > 0;
	}

	@Override
	public boolean view(Long id, String type) {
		Long userId = AuthUtil.getUserId();
		String ip = WebUtil.getIP();

		int result = mapper.insertView(id, type, userId, ip);
		log.debug("用户 {} 浏览 {} ID: {}, IP: {}, 结果: {}", userId, type, id, ip, result > 0);
		return result > 0;
	}

	@Override
	public boolean isLiked(Long id, String type) {
		Long userId = AuthUtil.getUserId();
		int exists = mapper.checkLikeExists(id, type, userId);
		return exists > 0;
	}

	@Override
	public boolean isFavorited(Long id, String type) {
		Long userId = AuthUtil.getUserId();
		int exists = mapper.checkFavoriteExists(id, type, userId);
		return exists > 0;
	}
}
