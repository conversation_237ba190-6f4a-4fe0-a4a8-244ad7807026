package org.springblade.miniapp.mapper;

/**
 * 数据操作 Mapper 接口
 *
 * <AUTHOR>
 * @version 1.0
 * @email <EMAIL>
 * @website duofan.top
 * @date 2025/7/31
 */
public interface DataOperateMapper {

	// 点赞相关操作
	int insertLike(Long id, String type, Long userId);
	int deleteLike(Long id, String type, Long userId);
	int checkLikeExists(Long id, String type, Long userId);

	// 收藏相关操作
	int insertFavorite(Long id, String type, Long userId);
	int deleteFavorite(Long id, String type, Long userId);
	int checkFavoriteExists(Long id, String type, Long userId);

	// 浏览记录
	int insertView(Long id, String type, Long userId, String ip);

	// 分享记录
	int insertShare(Long id, String type, Long userId, String ip);

}
