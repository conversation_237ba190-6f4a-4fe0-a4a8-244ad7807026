package org.springblade.miniapp.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;

import lombok.AllArgsConstructor;
import org.springblade.common.anno.AccessLimit;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.business.post.vo.SupPostVO;
import org.springblade.miniapp.service.WeChatPostService;
import org.springframework.web.bind.annotation.*;

@RestController
@AllArgsConstructor
@RequestMapping("/blade-chat/post")
@io.swagger.v3.oas.annotations.tags.Tag(name = "小程序拨号记录接口")
public class WeChatCallController {

	private final WeChatPostService postService;

	@GetMapping("/call-history")
	@AccessLimit(seconds = 30)
	@Operation(summary = "获取拨号记录")
	public R<IPage<SupPostVO>> getCallHistory(Query query) {
		return R.data(postService.getCallHistory(query));
	}

	@PostMapping("/clear-call-history")
	@Operation(summary = "清空拨号记录")
	@AccessLimit(seconds = 30)
	public R<Boolean> clearCallHistory() {
		return R.data(postService.clearCallHistory());
	}

	@PostMapping("/add-call-history/{postId}")
	@AccessLimit(seconds = 30)
	@Operation(summary = "添加拨号记录")
	public R<Boolean> addCallHistory(@PathVariable Long postId) {
		return R.data(postService.addCallHistory(postId));
	}
}
