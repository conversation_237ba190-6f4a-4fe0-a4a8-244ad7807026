package org.springblade.miniapp.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import org.springblade.business.points.dto.SigninDTO;
import org.springblade.business.points.service.ISigninService;
import org.springblade.business.points.vo.SigninVO;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 小程序签到控制器
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-chat/signin")
@Tag(name = "小程序签到接口", description = "小程序签到相关接口")
public class WeChatSigninController {

    private final ISigninService signinService;


    /**
     * 获取签到信息
     */
    @GetMapping("/info")
    @Operation(summary = "获取签到信息", description = "获取用户签到信息")
    public R<Map<String, Object>> getSigninInfo() {
		String userId = AuthUtil.getUserId().toString();
        try {
            // 获取签到统计信息
            Map<String, Object> stats = signinService.getSigninStats(userId);

            // 获取连续签到天数
            Integer continuousDays = signinService.getContinuousDays(userId);

            // 获取连续签到奖励配置
            List<Map<String, Object>> continuousRewards = signinService.getContinuousRewards();

            // 组装返回数据
            Map<String, Object> result = new HashMap<>();
            Boolean todaySigned = (Boolean) stats.get("todaySigned");
            result.put("todaySigned", todaySigned != null ? todaySigned : false);
            result.put("continuousDays", continuousDays != null ? continuousDays : 0);
            result.put("totalDays", stats.get("totalSigninDays"));
            result.put("lastSigninDate", stats.get("lastSigninDate"));
            result.put("signinReward", 10); // 基础签到奖励
            result.put("continuousRewards", continuousRewards);

            // 添加调试日志
            System.out.println("签到信息调试 - userId: " + userId);
            System.out.println("todaySigned: " + todaySigned);
            System.out.println("continuousDays: " + continuousDays);
            System.out.println("stats: " + stats);

            return R.data(result);
        } catch (Exception e) {
            return R.fail("获取签到信息失败：" + e.getMessage());
        }
    }

	/**
     * 获取连续签到天数
     */
	@GetMapping("/continuous")
	@Operation(summary = "获取连续签到天数", description = "获取用户连续签到天数")
	public R<Integer> getContinuousDays() {
		String userId = AuthUtil.getUserId().toString();
        try {
            return R.data(signinService.getContinuousDays(userId));
        } catch (Exception e) {
            return R.fail("获取连续签到天数失败：" + e.getMessage());
        }
    }

    /**
     * 执行签到
     */
    @PostMapping("/do")
    @Operation(summary = "执行签到", description = "用户执行签到操作")
    public R<Map<String, Object>> doSignin(HttpServletRequest request) {
        try {
            // 创建签到DTO
            SigninDTO signinDTO = new SigninDTO();

            // 设置签到IP
            String clientIp = getClientIp(request);
            signinDTO.setSigninIp(clientIp);

            // 设置设备信息
            String userAgent = request.getHeader("User-Agent");
            signinDTO.setDeviceInfo(userAgent);

			//用户Id
			Long userId = AuthUtil.getUserId();
			signinDTO.setUserId(userId.toString());

            SigninVO result = signinService.doSignin(signinDTO);

            // 组装返回数据，确保字段名与前端一致
            Map<String, Object> response = new HashMap<>();
            response.put("reward", result.getPoints()); // 基础奖励积分
            response.put("continuousReward", result.getContinuousReward()); // 连续签到奖励
            response.put("continuousDays", result.getContinuousDays()); // 连续签到天数
            response.put("totalDays", result.getTotalSigninDays()); // 总签到天数
            response.put("currentPoints", result.getCurrentPoints()); // 当前积分

            return R.data(response);
        } catch (Exception e) {
            return R.fail("签到失败：" + e.getMessage());
        }
    }

    /**
     * 补签
     */
    @PostMapping("/makeup")
    @Operation(summary = "补签", description = "用户补签指定日期")
    public R<SigninVO> makeUpSignin(@RequestParam String date, HttpServletRequest request) {
        if (Func.isEmpty(date)) {
            return R.fail("补签日期不能为空");
        }
		// 设置签到IP
		String clientIp = getClientIp(request);

		// 设置设备信息
		String userAgent = request.getHeader("User-Agent");

		String userId = AuthUtil.getUserId().toString();
        try {
            SigninVO result = signinService.makeUpSignin(userId, date,userAgent,clientIp );
            return R.data(result);
        } catch (Exception e) {
            return R.fail("补签失败：" + e.getMessage());
        }
    }

    /**
     * 获取月签到记录
     */
    @GetMapping("/record")
    @Operation(summary = "获取月签到记录", description = "获取用户某月的签到记录")
    public R<List<Map<String, Object>>> getMonthSigninRecord(
            @RequestParam Integer year,
            @RequestParam Integer month) {
        if (year == null || month == null) {
            return R.fail("年份和月份不能为空");
        }
		String userId = AuthUtil.getUserId().toString();
        try {
            List<Map<String, Object>> records = signinService.getMonthSigninRecord(userId, year, month);
            return R.data(records);
        } catch (Exception e) {
            return R.fail("获取签到记录失败：" + e.getMessage());
        }
    }

    /**
     * 获取连续签到奖励配置
     */
    @GetMapping("/rewards")
    @Operation(summary = "获取连续签到奖励配置", description = "获取连续签到奖励配置")
    public R<List<Map<String, Object>>> getContinuousRewards() {
        try {
            List<Map<String, Object>> rewards = signinService.getContinuousRewards();
            return R.data(rewards);
        } catch (Exception e) {
            return R.fail("获取奖励配置失败：" + e.getMessage());
        }
    }

    /**
     * 获取签到统计
     */
    @GetMapping("/stats")
    @Operation(summary = "获取签到统计", description = "获取用户签到统计信息")
    public R<Map<String, Object>> getSigninStats() {

		String userId = AuthUtil.getUserId().toString();
        try {
            Map<String, Object> stats = signinService.getSigninStats(userId);
            return R.data(stats);
        } catch (Exception e) {
            return R.fail("获取签到统计失败：" + e.getMessage());
        }
    }


    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String xip = request.getHeader("X-Real-IP");
        String xfor = request.getHeader("X-Forwarded-For");
        if (Func.isNotEmpty(xfor) && !"unKnown".equalsIgnoreCase(xfor)) {
            // 多次反向代理后会有多个ip值，第一个ip才是真实ip
            int index = xfor.indexOf(",");
            if (index != -1) {
                return xfor.substring(0, index);
            } else {
                return xfor;
            }
        }
        xfor = xip;
        if (Func.isNotEmpty(xfor) && !"unKnown".equalsIgnoreCase(xfor)) {
            return xfor;
        }
        if (Func.isEmpty(xfor) || "unknown".equalsIgnoreCase(xfor)) {
            xfor = request.getHeader("Proxy-Client-IP");
        }
        if (Func.isEmpty(xfor) || "unknown".equalsIgnoreCase(xfor)) {
            xfor = request.getHeader("WL-Proxy-Client-IP");
        }
        if (Func.isEmpty(xfor) || "unknown".equalsIgnoreCase(xfor)) {
            xfor = request.getHeader("HTTP_CLIENT_IP");
        }
        if (Func.isEmpty(xfor) || "unknown".equalsIgnoreCase(xfor)) {
            xfor = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (Func.isEmpty(xfor) || "unknown".equalsIgnoreCase(xfor)) {
            xfor = request.getRemoteAddr();
        }
        return xfor;
    }



/*	*//**
	 * 签到抽奖
	 *//*
	@PostMapping("/lottery")
	@Operation(summary = "签到抽奖", description = "根据用户签到次数进行抽奖，签到越多中奖概率越高")
	@AccessLimit // 限制访问频率
	public R<Map<String, Object>> lottery() {
		try {
			String userId = AuthUtil.getUserId().toString();

			// 获取用户签到统计信息
			Map<String, Object> stats = signinService.getSigninStats(userId);
			Integer totalSigninDays = (Integer) stats.get("totalSigninDays");

			// 默认签到天数为0
			if (totalSigninDays == null) {
				totalSigninDays = 0;
			}

			// 根据签到天数计算中奖概率，签到越多中奖概率越高
			// 基础概率10%，每签到1天增加0.5%中奖率，最高不超过50%
			double baseRate = 0.1;
			double ratePerDay = 0.005;
			double maxRate = 0.5;

			double winRate = Math.min(baseRate + (totalSigninDays * ratePerDay), maxRate);

			// 生成随机数判断是否中奖
			boolean isWin = Math.random() < winRate;

			// 奖励积分（随机10-100分）
			int rewardPoints = 0;
			if (isWin) {
				rewardPoints = (int) (Math.random() * 91) + 10; // 10-100随机
			}

			// 如果中奖，更新用户积分
			if (isWin) {
				// 这里应该调用积分服务增加用户积分
				// 为简化实现，我们只返回结果，实际应用中需要更新用户积分
			}

			// 组装返回结果
			Map<String, Object> result = new HashMap<>();
			result.put("isWin", isWin);
			result.put("winRate", Math.round(winRate * 10000) / 100.0); // 转换为百分比形式，保留2位小数
			result.put("rewardPoints", rewardPoints);
			result.put("totalSigninDays", totalSigninDays);

			return R.data(result);
		} catch (Exception e) {
			return R.fail("抽奖失败：" + e.getMessage());
		}
	}*/


	/**
	 * 查询用户是否是否中奖
	 */
	@GetMapping("/queryUserWin")
	public R<Boolean> queryUserWin() {
		return R.data(signinService.IsWin());
	}

	/**
	 * 获取签到记录列表（分页）
	 */
	@GetMapping("/records")
	@Operation(summary = "获取签到记录列表", description = "分页获取用户签到记录列表，包含签到时间和获得积分")
	public R<Map<String, Object>> getSigninRecords(
			@RequestParam(defaultValue = "1") Integer page,
			@RequestParam(defaultValue = "20") Integer size,
			@RequestParam(required = false) String startDate,
			@RequestParam(required = false) String endDate) {

		String userId = AuthUtil.getUserId().toString();
		try {
			Map<String, Object> result = signinService.getSigninRecords(userId, page, size, startDate, endDate);
			return R.data(result);
		} catch (Exception e) {
			return R.fail("获取签到记录失败：" + e.getMessage());
		}
	}

	/**
	 * 获取签到统计汇总
	 */
	@GetMapping("/summary")
	@Operation(summary = "获取签到统计汇总", description = "获取用户签到统计汇总信息")
	public R<Map<String, Object>> getSigninSummary() {
		String userId = AuthUtil.getUserId().toString();
		try {
			Map<String, Object> summary = signinService.getSigninSummary(userId);
			return R.data(summary);
		} catch (Exception e) {
			return R.fail("获取签到统计汇总失败：" + e.getMessage());
		}
	}
}
