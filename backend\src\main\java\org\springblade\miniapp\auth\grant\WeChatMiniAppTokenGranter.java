package org.springblade.miniapp.auth.grant;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.util.WxMaConfigHolder;
import cn.hutool.core.util.StrUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.social.props.SocialProperties;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.WebUtil;
import org.springblade.miniapp.config.WxMaProperties;
import org.springblade.modules.auth.granter.ITokenGranter;
import org.springblade.modules.auth.granter.TokenParameter;
import org.springblade.modules.auth.utils.TokenUtil;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.entity.UserInfo;
import org.springblade.modules.system.entity.UserOauth;
import org.springblade.modules.system.service.IUserService;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@AllArgsConstructor
public class WeChatMiniAppTokenGranter implements ITokenGranter {
	WxMaProperties wxMaProperties;

	private final WxMaService wxMaService;

	public static final String GRANT_TYPE = "wechat_mini_app";

	private static final Integer AUTH_SUCCESS_CODE = 2000;

//	private final IUserClient userClient;
	private final IUserService userService;
	private final SocialProperties socialProperties;

	public WxMaJscode2SessionResult login(String appid, String code) {
		if (StrUtil.isBlank(code)) {
			throw new IllegalArgumentException("传参出错");
		}

		if (!wxMaService.switchover(appid)) {
			throw new IllegalArgumentException(String.format("未找到对应appid=[%s]的配置，请核实！", appid));
		}

		try {
			WxMaJscode2SessionResult session = wxMaService.getUserService().getSessionInfo(code);
			log.info(session.getSessionKey());
			log.info(session.getOpenid());
			//TODO 可以增加自己的逻辑，关联业务相关数据
			return session;
		} catch (WxErrorException e) {
			log.error(e.getMessage(), e);
			throw new ServiceException(e.getMessage());
		} finally {
			WxMaConfigHolder.remove();//清理ThreadLocal
		}
	}

	@Override
	public UserInfo grant(TokenParameter tokenParameter) {
		HttpServletRequest request = WebUtil.getRequest();
		String tenantId = Func.toStr(request.getHeader(TokenUtil.TENANT_HEADER_KEY), TokenUtil.DEFAULT_TENANT_ID);
		// 开放平台授权码
		String code = request.getParameter("code");
		// 获取开放平台授权数据
		WxMaJscode2SessionResult loginRes = login(wxMaProperties.getMainAppId(), code);

		UserOauth userOauth = new UserOauth();
		userOauth.setSource(GRANT_TYPE);
		userOauth.setTenantId(tenantId);
		userOauth.setUuid(loginRes.getOpenid());
		userOauth.setSource(GRANT_TYPE);
		UserInfo data = userService.userAuthInfoByMiniApp(userOauth);
		data.setSessionKey(loginRes.getSessionKey());
		User user = data.getUser();
		user.setAccount(userOauth.getUuid());
		return data;
	}
}
