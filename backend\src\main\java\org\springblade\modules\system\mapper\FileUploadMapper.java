/**
 * Copyright (c) 2018-2099, Chill <PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.modules.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.modules.system.entity.FileUpload;
import org.springblade.modules.system.vo.FileUploadVO;

import java.util.List;
import java.util.Map;

/**
 * 文件上传Mapper接口
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
public interface FileUploadMapper extends BaseMapper<FileUpload> {

    /**
     * 分页查询文件上传
     *
     * @param page       分页参数
     * @param fileUpload 查询条件
     * @return 分页结果
     */
    List<FileUploadVO> selectFileUploadPage(IPage<FileUploadVO> page, @Param("fileUpload") FileUploadVO fileUpload);

    /**
     * 获取文件总大小
     *
     * @return 总大小
     */
    Long getTotalFileSize();

    /**
     * 按文件类型统计
     *
     * @return 统计结果
     */
    List<Map<String, Object>> getFileTypeStats();

    /**
     * 按上传来源统计
     *
     * @return 统计结果
     */
    List<Map<String, Object>> getUploadSourceStats();
}
