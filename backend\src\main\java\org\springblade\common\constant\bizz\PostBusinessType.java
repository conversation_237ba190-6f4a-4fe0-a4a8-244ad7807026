package org.springblade.common.constant.bizz;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * 业务类型
 *
 * <AUTHOR>
 * @version 1.0
 * @email <EMAIL>
 * @website duofan.top
 * @date 2025/7/24
 */
@Getter
@AllArgsConstructor
public enum PostBusinessType {
	CARPOOL("0","顺丰车"),
	JOB_SEEKING("1","简历帖"),
	JOB_OFFER("2","招聘贴"),
	RENT_HOUSE("3","租房贴"),
	USED_CAR("4","二手车");

	private final String value;
	private final String label;
}
