package org.springblade.miniapp.service.impl;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import org.springblade.business.painpoint.entity.PainPoint;
import org.springblade.business.painpoint.service.IPainPointService;
import org.springblade.business.post.dto.CommentDTO;
import org.springblade.business.post.entity.CommentLike;
import org.springblade.business.post.entity.CommentMention;
import org.springblade.business.post.entity.Feedback;
import org.springblade.business.post.entity.SupPost;
import org.springblade.business.post.service.ISupPostService;
import org.springblade.business.user.mapper.FeedbackMapper;
import org.springblade.business.post.service.IFeedbackService;
import org.springblade.business.post.vo.FeedbackVO;
import org.springblade.business.user.service.IWeUserService;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.miniapp.service.WeChatFeedbackService;
import org.springblade.modules.system.service.IParamService;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.metadata.IPage;

import lombok.AllArgsConstructor;

@Service
@AllArgsConstructor
public class WeChatFeedbackServiceImpl implements WeChatFeedbackService {

	private final IFeedbackService feedbackService;
	private final FeedbackMapper feedbackMapper;
	private IParamService paramService;
	private IPainPointService painPointService;
	private ISupPostService postService;

	@Override
	public Long submit(Feedback feedback) {
		feedback.setUserId(AuthUtil.getUserId());
		feedback.setAuditStatus("0");
		//判断是否是作者本人
		SupPost post =postService.getOne(new LambdaQueryWrapper<SupPost>().eq(SupPost::getId, feedback.getRelevancyId()));
		if(post.getCreateUser().equals(AuthUtil.getUserId())){
			feedback.setIsAuthor(1);
		}
		feedbackService.save(feedback);
		return feedback.getId();
	}



	@Override
	public boolean remove(String ids) {
		return feedbackService.removeByUserId(Func.toLongList(ids));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean toggleHelpful(Long id) {
		Map<String, Object> model = Map.of("feedbackId", id, "userId", AuthUtil.getUserId());
		try {
			feedbackMapper.InsertFeedbackHelpful(model);
		} catch (DuplicateKeyException e) {
			throw new ServiceException("请勿重复标记");
		}
		return true;
	}

	@Override
	public IPage<FeedbackVO> page(IPage<FeedbackVO> page, FeedbackVO feedback) {
		if(isOpenPreAudit()){
			feedback.setAuditStatus("1");
		}
		// 只查询顶级评论（非回复）
		feedback.setParentId(null);
		feedback.setUserId(AuthUtil.getUserId());
		return feedbackMapper.selectFeedbackPage(page, feedback);
	}

	/**
	 * 新增程序反馈
	 * @param painPoint
	 * @return
	 */
	public boolean save(PainPoint painPoint) {
		painPoint.setAuditStatus("0");
		return painPointService.save(painPoint);
	}

	private boolean isOpenPreAudit() {
		String value = paramService.getByKey("post.preAudit.enabled");
		return "true".equals(value);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Long addComment(CommentDTO commentDTO) {
		// 创建评论对象
		Feedback comment = new Feedback();
		BeanUtil.copyProperties(commentDTO, comment);

		// 设置评论基本信息
		comment.setUserId(AuthUtil.getUserId());
		comment.setCommentType("COMMENT");
		comment.setLevel(1);
		comment.setReplyCount(0);
		comment.setAuditStatus("0"); // 待审核

		// 保存评论
		feedbackService.save(comment);

		// 处理@用户功能
		if (commentDTO.getMentionedUserIds() != null && !commentDTO.getMentionedUserIds().isEmpty()) {
			saveMentionedUsers(comment.getId(), commentDTO.getMentionedUserIds(), commentDTO.getMentionedUsers());
		}

		return comment.getId();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Long replyComment(CommentDTO commentDTO) {
		// 验证父评论ID是否为空
		if (commentDTO.getParentId() == null || commentDTO.getParentId().trim().isEmpty()) {
			throw new ServiceException("父评论ID不能为空");
		}

		// 将字符串ID转换为Long
		Long parentId;
		try {
			parentId = Long.valueOf(commentDTO.getParentId());
		} catch (NumberFormatException e) {
			throw new ServiceException("父评论ID格式错误");
		}

		// 验证父评论是否存在
		Feedback parentComment = feedbackService.getById(parentId);
		if (parentComment == null) {
			throw new ServiceException("父评论不存在");
		}

		// 创建回复对象
		Feedback reply = new Feedback();

		// 手动设置字段，避免类型转换问题
		reply.setRelevancyId(Long.valueOf(commentDTO.getPostId()));
		reply.setParentId(parentId);
		reply.setContent(commentDTO.getContent());
		reply.setImage(commentDTO.getImage());
		reply.setContactInfo(commentDTO.getContactInfo());

		// 设置被回复用户信息
		if (commentDTO.getReplyToUserId() != null && !commentDTO.getReplyToUserId().trim().isEmpty()) {
			reply.setReplyToUserId(Long.valueOf(commentDTO.getReplyToUserId()));
		}
		reply.setReplyToUserName(commentDTO.getReplyToUserName());

		// 设置回复基本信息
		reply.setUserId(AuthUtil.getUserId());
		reply.setCommentType("REPLY");
		reply.setLevel(parentComment.getLevel() + 1);
		reply.setReplyCount(0);
		reply.setAuditStatus("0"); // 待审核

		//判断是否是作者本人
		SupPost post =postService.getOne(new LambdaQueryWrapper<SupPost>().eq(SupPost::getId, reply.getRelevancyId()));
		if(post.getCreateUser().equals(AuthUtil.getUserId())){
			reply.setIsAuthor(1);
		}
		// 保存回复
		feedbackService.save(reply);

		// 更新父评论的回复数量
		parentComment.setReplyCount(parentComment.getReplyCount() + 1);
		feedbackService.updateById(parentComment);

		// 处理@用户功能
		if (commentDTO.getMentionedUserIds() != null && !commentDTO.getMentionedUserIds().isEmpty()) {
			// 将字符串ID转换为Long类型
			List<Long> mentionedUserIds = commentDTO.getMentionedUserIds().stream()
				.map(Long::valueOf)
				.collect(Collectors.toList());
			saveMentionedUsers(reply.getId(), mentionedUserIds, commentDTO.getMentionedUsers());
		}

		return reply.getId();
	}

	@Override
	public IPage<FeedbackVO> getCommentList(Long postId, Query query) {
		FeedbackVO feedback = new FeedbackVO();
		feedback.setRelevancyId(postId);
		feedback.setCommentType("COMMENT"); // 只查询顶级评论
		// 暂时移除审核状态过滤，用于测试
		// if (isOpenPreAudit()) {
		// 	feedback.setAuditStatus("1");
		// }

		IPage<FeedbackVO> page = Condition.getPage(query);
		return feedbackMapper.selectFeedbackPage(page, feedback);
	}

	@Override
	public List<FeedbackVO> getCommentReplies(Long parentId) {
		FeedbackVO feedback = new FeedbackVO();
		feedback.setParentId(parentId);
		if (isOpenPreAudit()) {
			feedback.setAuditStatus("1");
		}

		// 使用分页查询但不限制数量，获取所有回复
		Page<FeedbackVO> page = new Page<>(1, 1000);
		IPage<FeedbackVO> result = feedbackMapper.selectFeedbackPage(page, feedback);
		return result.getRecords();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean removeComment(Long commentId) {
		// 验证评论是否属于当前用户
		Feedback comment = feedbackService.getById(commentId);
		if (comment == null) {
			throw new ServiceException("评论不存在");
		}

		if (!comment.getUserId().equals(AuthUtil.getUserId())) {
			throw new ServiceException("只能删除自己的评论");
		}

		// 删除评论及其所有回复
		return feedbackService.removeById(commentId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean toggleCommentLike(Long commentId, String likeType) {
		Map<String, Object> model = Map.of(
			"commentId", commentId,
			"userId", AuthUtil.getUserId(),
			"likeType", likeType
		);

		try {
			// 尝试插入点赞记录
			feedbackMapper.insertCommentLike(model);
			return true;
		} catch (DuplicateKeyException e) {
			// 如果已经点赞，则取消点赞
			feedbackMapper.deleteCommentLike(model);
			return false;
		}
	}

	/**
	 * 保存被提及的用户
	 */
	private void saveMentionedUsers(Long commentId, List<Long> mentionedUserIds, List<CommentDTO.MentionedUserInfo> mentionedUsers) {
		if (mentionedUserIds == null || mentionedUserIds.isEmpty()) {
			return;
		}

		for (int i = 0; i < mentionedUserIds.size(); i++) {
			Long userId = mentionedUserIds.get(i);
			String nickname = (mentionedUsers != null && i < mentionedUsers.size())
				? mentionedUsers.get(i).getNickname()
				: "";

			CommentMention mention = new CommentMention();
			mention.setCommentId(commentId);
			mention.setMentionedUserId(userId);
			mention.setMentionedUserName(nickname);
			mention.setCreateUser(AuthUtil.getUserId());

			// 这里需要创建CommentMention的Service来保存
			// commentMentionService.save(mention);
		}
	}
}
