package org.springblade.miniapp.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springblade.common.anno.AccessLimit;
import org.springblade.common.constant.bizz.MessageTypeEnum;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.business.post.dto.MessageCreateDTO;
import org.springblade.business.post.dto.MessageQueryDTO;
import org.springblade.business.post.dto.MessageTemplateCreateDTO;
import org.springblade.business.post.dto.MessageTemplateUpdateDTO;
import org.springblade.business.post.entity.UrbMessage;
import org.springblade.business.post.entity.UrbMessageTemplate;
import org.springblade.business.post.mapper.UrbMessageMapper;
import org.springblade.business.post.mapper.UrbMessageTemplateMapper;
import org.springblade.miniapp.service.WeChatMessageService;
import org.springblade.miniapp.vo.MessageTemplateVO;
import org.springblade.miniapp.vo.MessageVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@RequiredArgsConstructor
public class WeChatMessageServiceImpl implements WeChatMessageService {

	private final UrbMessageMapper messageMapper;
	private final UrbMessageTemplateMapper templateMapper;

	/**
	 * 分页条件查询消息
	 */
	@AccessLimit(seconds = 30)
	@Override
	public IPage<MessageVO> queryMessages(MessageQueryDTO queryDTO) {
		queryDTO.setUserId(AuthUtil.getUserId());
		Page<UrbMessage> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
		List<UrbMessage> messages = messageMapper.selectByCondition(queryDTO);
		long total = page.getTotal();
		// VO转换
		List<MessageVO> voList = messages.stream().map(this::toVO).toList();
		Page<MessageVO> voPage = new Page<>(queryDTO.getCurrent(), queryDTO.getSize(), total);
		voPage.setRecords(voList);
		return voPage;
	}

	/**
	 * 标记消息为已读
	 */
	@Override
	@Transactional
	public void markAsRead(Long id) {
		//鉴权判断操作这是不是本人
		if (!AuthUtil.getUserId().equals(messageMapper.selectById(id).getUserId())){
			throw new RuntimeException("无权操作");
		}
		UrbMessage message = new UrbMessage();
		message.setId(id);
		message.setIsRead(true);
		messageMapper.updateById(message);
	}

	/**
	 * 批量删除消息
	 */
	@Override
	@Transactional
	public void batchDelete(List<Long> ids) {
		//鉴权判断操作这是不是本人
		if (ids == null || ids.isEmpty()) return;
		for (Long id : ids) {
			if (!AuthUtil.getUserId().equals(messageMapper.selectById(id).getUserId())) {
				throw new RuntimeException("无权操作");
			}
		}
		messageMapper.deleteBatchIds(ids);

	}

	/**
	 * 创建消息（根据模板渲染）
	 */
	@Override
	@Transactional
	public MessageVO createMessage(MessageCreateDTO createDTO) {
		// 查询模板
		UrbMessageTemplate template = templateMapper.selectByTypeCode(createDTO.getMessageType());
		if (template == null || template.getStatus() == null || template.getStatus() != 1) {
			throw new RuntimeException("消息模板不存在或已禁用");
		}
		// 渲染模板
		String title = renderTemplate(template.getTitleTemplate(), createDTO.getTemplateParams());
		String content = renderTemplate(template.getContentTemplate(), createDTO.getTemplateParams());
		// 创建消息
		UrbMessage message = new UrbMessage();
		message.setUserId(createDTO.getUserId());
		message.setTitle(title);
		message.setContent(content);
		message.setMessageType(createDTO.getMessageType());
		message.setIsRead(false);
		message.setRelatedId(createDTO.getRelatedId());
		messageMapper.insert(message);
		return toVO(message);
	}

	/**
	 * 创建消息（根据userId、MessageTypeEnum、templateParams、relatedId）
	 */
	@Override
	@Transactional
	public void createMessage(Long userId, MessageTypeEnum type, java.util.Map<String, Object> templateParams, Long relatedId) {
		MessageCreateDTO dto = new MessageCreateDTO();
		dto.setUserId(userId);
		dto.setMessageType(type.getCode());
		dto.setTemplateParams(templateParams);
		dto.setRelatedId(relatedId);
		this.createMessage(dto);
	}

	/**
	 * 获取所有消息模板，管理员可见
	 */
	@Override
	public List<MessageTemplateVO> getAllTemplates() {
		//判断是否是管理员
		if (!AuthUtil.isAdministrator()){
			throw new RuntimeException("无权限");
		}
		List<UrbMessageTemplate> templates = templateMapper.selectList(null);
		return templates.stream().map(this::toTemplateVO).toList();
	}

	/**
	 * 更新消息模板
	 */
	@Override
	@Transactional
	public void updateTemplate(MessageTemplateUpdateDTO updateDTO) {
		//判断是否是管理员
		if (!AuthUtil.isAdministrator()){
			throw new RuntimeException("无权限");
		}
		UrbMessageTemplate template = new UrbMessageTemplate();
		BeanUtils.copyProperties(updateDTO, template);
		templateMapper.updateById(template);
	}

	/**
	 * 添加消息模板
	 */
	@Override
	@Transactional
	public void addTemplate(MessageTemplateCreateDTO createDTO) {
		//判断是否是管理员
		if (!AuthUtil.isAdministrator()){
			throw new RuntimeException("无权限");
		}
		UrbMessageTemplate template = new UrbMessageTemplate();
		org.springframework.beans.BeanUtils.copyProperties(createDTO, template);
		templateMapper.insert(template);
	}

	/**
	 * 渲染模板内容
	 */
	private String renderTemplate(String template, java.util.Map<String, Object> params) {
		if (params == null) return template;
		String result = template;
		for (var entry : params.entrySet()) {
			String key = "{" + entry.getKey() + "}";
			result = result.replace(key, String.valueOf(entry.getValue()));
		}
		return result;
	}

	/**
	 * 实体转VO
	 */
	private MessageVO toVO(UrbMessage message) {
		if (message == null) return null;
		MessageVO vo = new MessageVO();
		BeanUtils.copyProperties(message, vo);
		return vo;
	}

	private MessageTemplateVO toTemplateVO(UrbMessageTemplate template) {
		if (template == null) return null;
		MessageTemplateVO vo = new MessageTemplateVO();
		BeanUtils.copyProperties(template, vo);
		return vo;
	}
}
