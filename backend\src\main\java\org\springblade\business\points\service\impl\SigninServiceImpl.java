package org.springblade.business.points.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.business.points.dto.SigninDTO;
import org.springblade.business.points.entity.PointsRecord;
import org.springblade.business.points.entity.SigninRecord;
import org.springblade.business.points.mapper.PointsRecordMapper;
import org.springblade.business.points.mapper.SigninRecordMapper;
import org.springblade.business.points.service.ISigninService;
import org.springblade.business.points.vo.SigninRecordVO;
import org.springblade.business.points.vo.SigninVO;
import org.springblade.business.user.entity.WeUser;
import org.springblade.business.user.service.IWeUserService;
import org.springblade.core.secure.utils.AuthUtil;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
/**
 * 签到服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
@AllArgsConstructor
public class SigninServiceImpl extends ServiceImpl<SigninRecordMapper, SigninRecord> implements ISigninService {


	private PointsRecordMapper pointsRecordMapper;

	private IWeUserService weUserService;
	// 基础签到积分
    private static final int BASE_SIGNIN_POINTS = 10;

	private RedisTemplate<String ,Object> redisTemplate;

    // 连续签到奖励配置
    private static final List<Map<String, Object>> CONTINUOUS_REWARDS = Arrays.asList(
        Map.of("days", 1, "reward", 10),
        Map.of("days", 3, "reward", 15),
        Map.of("days", 7, "reward", 25),
        Map.of("days", 15, "reward", 50),
        Map.of("days", 30, "reward", 100)
    );

    @Override
    public IPage<SigninRecordVO> selectSigninRecordPage(IPage<SigninRecordVO> page, SigninRecord signinRecord) {
        return baseMapper.selectSigninRecordPage(page, signinRecord);
    }

    @Override
    public List<SigninRecordVO> getSigninInfo(String userId) {
        try {
			List<SigninRecord> monthSigninRecords = baseMapper.selectByMonth(userId, LocalDate.now().getYear(), LocalDate.now().getMonthValue());
			List<SigninRecordVO> signinRecordVOList = new ArrayList<>();
/*			for(SigninRecord signinRecord : monthSigninRecords){
				SigninRecordVO signinRecordVO = new SigninRecordVO();
				signinRecordVO.setId(signinRecord.getId());
				signinRecordVO.setUserId(userId);
				signinRecordVO.setSigninDate(signinRecord.getSigninDate());
				signinRecordVO.setSigninTime(signinRecord.getSigninTime());
				signinRecordVO.setPoints(signinRecord.getPoints());
				signinRecordVO.setContinuousDays(signinRecord.getContinuousDays());
				signinRecordVO.setSigninType(signinRecord.getSigninType());
				signinRecordVOList.add(signinRecordVO);
			}*/
            return signinRecordVOList;
        } catch (Exception e) {
            log.error("获取签到信息异常，userId: {}", userId, e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SigninVO doSignin(SigninDTO signinDTO) {
        if (signinDTO == null || StrUtil.isBlank(signinDTO.getUserId())) {
            throw new RuntimeException("签到参数错误");
        }

        String userId = signinDTO.getUserId();
        LocalDate signinDate = LocalDate.now();

        try {
            // 检查今日是否已签到
            if (isTodaySigned(userId)) {
                throw new RuntimeException("今日已签到，请明天再来");
            }

/*            // 获取用户积分信息
            UserPoints userPoints = baseMapper.getUserPointsByuserId(userId);
            if (userPoints == null) {
                userPointsService.initUserPoints(userId, "", "");
                userPoints = userPointsService.getUserPointsByuserId(userId);
            }*/
//
           // 计算连续签到天数
            int continuousDays = calculateContinuousDays(userId, signinDate);
			// 计算连续签到奖励
			Integer continuousReward=calculateContinuousReward(continuousDays);
			// 计算总签到天数
            Integer totalSigninDays =baseMapper.countMonthSigninDays(userId,signinDate.getYear(),signinDate.getMonthValue()) + 1;

            // 计算签到奖励
            int basePoints = BASE_SIGNIN_POINTS;
//            int continuousReward = calculateContinuousReward(continuousDays);
//            int totalPoints = basePoints + continuousReward;

            // 创建签到记录
            SigninRecord signinRecord = new SigninRecord();
            signinRecord.setUserId(userId);
            signinRecord.setSigninDate(signinDate);
            signinRecord.setSigninTime(LocalDateTime.now());
            signinRecord.setYear(signinDate.getYear());
            signinRecord.setMonth(signinDate.getMonthValue());
            signinRecord.setDay(signinDate.getDayOfMonth());
            signinRecord.setWeekDay(signinDate.getDayOfWeek().getValue());
            signinRecord.setPoints(basePoints);
            signinRecord.setContinuousReward(continuousReward);
            signinRecord.setContinuousDays(continuousDays);
			signinRecord.setSigninType("NORMAL");
            signinRecord.setSigninIp(signinDTO.getSigninIp());
            signinRecord.setDeviceInfo(signinDTO.getDeviceInfo());
            signinRecord.setStatus(1);

            // 保存签到记录
            save(signinRecord);

            // 更新用户积分
//            userPointsService.addPoints(userId, totalPoints, "SIGNIN",
//                String.format("每日签到，连续%d天", continuousDays));

            // 更新用户签到信息
//            userPointsService.updateContinuousDays(userId, continuousDays);
//            userPoints.setTotalSigninDays(totalSigninDays);
//            userPoints.setLastSigninTime(signinDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
//            userPointsService.updateById(userPoints);

			//查询该用户信息
			WeUser user = weUserService.getById(userId);
			int balance =user.getBalance();
			user.setBalance(balance+basePoints+continuousReward);
			weUserService.updateById(user);
			//保存积分变动信息
			PointsRecord pointsRecord = new PointsRecord();
			pointsRecord.setUserId(userId);
			pointsRecord.setPoints(1);
			pointsRecord.setBeforePoints(balance);
			pointsRecord.setAfterPoints(balance+basePoints+continuousReward);
			pointsRecord.setType("0");
			pointsRecord.setBusinessId(signinRecord.getId());
			pointsRecord.setTypeName("签到");
			pointsRecord.setBusinessType("SIGNIN");
			pointsRecord.setDescription("签到所得基础积分+连续签到所得额外奖励积分");
			pointsRecordMapper.insert(pointsRecord);
			// 构建返回结果
            SigninVO signinVO = new SigninVO();
            signinVO.setId(signinRecord.getId());
            signinVO.setUserId(userId);
            signinVO.setSigninDate(signinDate);
            signinVO.setSigninTime(signinRecord.getSigninTime());
            signinVO.setPoints(basePoints);
            signinVO.setContinuousReward(continuousReward);
            signinVO.setContinuousDays(continuousDays);
            signinVO.setTotalSigninDays(totalSigninDays);
//            signinVO.setCurrentPoints(userPoints.getPoints() + totalPoints);
			signinVO.setSigninType(signinRecord.getSigninType());
            signinVO.setTodaySigned(true);
            signinVO.setLastSigninTime(signinDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));

//            log.info("用户签到成功，userId: {}, 获得积分: {}, 连续签到: {}天", userId, totalPoints, continuousDays);
            return signinVO;
        } catch (Exception e) {
            log.error("用户签到失败，userId: {}", userId, e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SigninVO makeUpSignin(String userId, String date, String deviceInfo,String signinIp) {
        if (StrUtil.isBlank(userId) || StrUtil.isBlank(date)) {
            throw new RuntimeException("补签参数错误");
        }

        try {
            LocalDate signinDate = LocalDate.parse(date);
            LocalDate today = LocalDate.now();

            // 只能补签过去的日期
            if (signinDate.isAfter(today)) {
                throw new RuntimeException("不能补签未来日期");
            }

            // 检查是否已签到
            if (isDateSigned(userId, signinDate)) {
                throw new RuntimeException("该日期已签到");
            }

            // 补签逻辑类似正常签到，但使用补签类型
            SigninDTO signinDTO = new SigninDTO();
            signinDTO.setUserId(userId);
            signinDTO.setSigninDate(date);
			signinDTO.setDeviceInfo(deviceInfo);
			signinDTO.setSigninIp(signinIp);
            signinDTO.setSigninType("MAKEUP");
            // 这里简化处理，实际补签可能需要消耗补签卡等道具
            return doSignin(signinDTO);
        } catch (Exception e) {
            log.error("用户补签失败，userId: {}, date: {}", userId, date, e);
            throw e;
        }
    }

    @Override
    public List<Map<String, Object>> getMonthSigninRecord(String userId, Integer year, Integer month) {
        if (StrUtil.isBlank(userId) || year == null || month == null) {
            return new ArrayList<>();
        }

        try {
            // 获取该月签到记录
            List<SigninRecord> records = baseMapper.selectByMonth(userId, year, month);

            // 构建日历数据
            List<Map<String, Object>> calendar = new ArrayList<>();
            LocalDate firstDay = LocalDate.of(year, month, 1);
            LocalDate lastDay = firstDay.plusMonths(1).minusDays(1);
            LocalDate today = LocalDate.now();

            // 填充上个月的日期
            LocalDate startDate = firstDay.minusDays(firstDay.getDayOfWeek().getValue() - 1);
            if (startDate.isBefore(firstDay)) {
                for (LocalDate date = startDate; date.isBefore(firstDay); date = date.plusDays(1)) {
                    Map<String, Object> day = new HashMap<>();
                    day.put("date", date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    day.put("day", date.getDayOfMonth());
                    day.put("isCurrentMonth", false);
                    day.put("isToday", false);
                    day.put("isSigned",isDateSigned(userId, date));
                    calendar.add(day);
                }
            }

            // 填充当前月的日期
            for (LocalDate date = firstDay; !date.isAfter(lastDay); date = date.plusDays(1)) {
                Map<String, Object> day = new HashMap<>();
                day.put("date", date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                day.put("day", date.getDayOfMonth());
                day.put("isCurrentMonth", true);
                day.put("isToday", date.equals(today));
                day.put("isSigned",false);
                calendar.add(day);
            }

			for (SigninRecord record : records) {
				calendar.stream().filter(day ->  LocalDate.parse((String)day.get("date")).equals(record.getSigninDate()))
					.forEach(day -> day.put("isSigned",true));
			}

            // 填充下个月的日期
            LocalDate endDate = lastDay.plusDays(7 - lastDay.getDayOfWeek().getValue());
            for (LocalDate date = lastDay.plusDays(1); !date.isAfter(endDate); date = date.plusDays(1)) {
                Map<String, Object> day = new HashMap<>();
                day.put("date", date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                day.put("day", date.getDayOfMonth());
                day.put("isCurrentMonth", false);
                day.put("isToday", false);
                day.put("isSigned", false);
                calendar.add(day);
            }

            return calendar;
        } catch (Exception e) {
            log.error("获取月签到记录异常，userId: {}, year: {}, month: {}", userId, year, month, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Map<String, Object>> getContinuousRewards() {
        // 获取连续签到奖励配置
		 return baseMapper.getAllContinuousReward();
    }

    @Override
    public Map<String, Object> getSigninStats(String userId) {
        if (StrUtil.isBlank(userId)) {
            return new HashMap<>();
        }

        try {
            Map<String, Object> stats = new HashMap<>();

            // 获取今日签到状态
            stats.put("todaySigned", isTodaySigned(userId));

            // 获取连续签到天数
            stats.put("continuousDays", getContinuousDays(userId));

            // 获取总签到天数（本月）
            List<SigninRecord> monthRecords = baseMapper.selectByMonth(userId,
                LocalDate.now().getYear(), LocalDate.now().getMonthValue());
            stats.put("totalSigninDays", monthRecords.size());

            // 获取最后签到日期
            SigninRecord lastRecord = baseMapper.selectLastSignin(userId);
            if (lastRecord != null) {
                stats.put("lastSigninDate", lastRecord.getSigninDate().toString());
            }

            return stats;
        } catch (Exception e) {
            log.error("获取签到统计异常，userId: {}", userId, e);
            return new HashMap<>();
        }
    }

    @Override
    public boolean isTodaySigned(String userId) {
        return isDateSigned(userId, LocalDate.now());
    }

	@Override
	public Integer getContinuousDays(String userId) {
		//查询最近一次签到
		SigninRecord record = baseMapper.selectLastSignin(userId);
		//检查今天是否已签
		 if(LocalDate.now().equals(record.getSigninDate())){
			 return record.getContinuousDays();
		 }
		 LocalDate yesterday =LocalDate.now().minusDays(1);
		 //判断最近一次签到是否为昨天
		 if(record==null||!record.getSigninDate().equals(yesterday)){
			 return 0;
		 }
		 return record.getContinuousDays();
	}

	/**
     * 检查指定日期是否已签到
     */
    private boolean isDateSigned(String userId, LocalDate date) {
        SigninRecord record = baseMapper.selectByDate(userId, date);
        return record != null;
    }

    /**
     * 计算连续签到天数
     */
    private int calculateContinuousDays(String userId, LocalDate signinDate) {
        try {
            SigninRecord lastSignin = baseMapper.selectLastSignin(userId);
            if (lastSignin == null) {
                return 1; // 首次签到
            }
            LocalDate lastSigninDate = lastSignin.getSigninDate();
            LocalDate yesterday = signinDate.minusDays(1);

            if (lastSigninDate.equals(yesterday)) {
                // 连续签到
                return lastSignin.getContinuousDays() + 1;
            } else if (lastSigninDate.equals(signinDate)) {
                // 同一天重复签到
                return lastSignin.getContinuousDays();
            } else {
                // 中断签到，重新开始
                return 1;
            }
        } catch (Exception e) {
            log.error("计算连续签到天数异常，userId: {}", userId, e);
            return 1;
        }
    }

    /**
     * 计算连续签到奖励
     */
    private int calculateContinuousReward(int continuousDays) {
/*        for (Map<String, Object> reward : CONTINUOUS_REWARDS) {
            int days = (Integer) reward.get("days");
            if (continuousDays == days) {
                return (Integer) reward.get("reward");
            }
        }*/
		return baseMapper.getContinuousReward(continuousDays)==null?0:baseMapper.getContinuousReward(continuousDays);
    }


/*	*//**
	 * 抽奖
	 *//*
	public Map<String, Object> getLottery(String startDate, String endDate) {
		//获取日期内所有签到记录
		List<Map<String, Object>> records =baseMapper.selectByDateRange(startDate,endDate);
		AtomicLong totalUser = new AtomicLong();
		AtomicLong totalSingins= new AtomicLong();
		records.stream().forEach(record -> {
			totalUser.getAndIncrement();
			totalSingins.addAndGet((Long) record.get("count"));
		});
		//根据总用户数和总签到数计算中奖概率，保证一定有一个人中奖，且签到次数多的人中奖率高
		records.stream().forEach(record -> {
			double probability = (double)record.get("count")/ totalSingins.get();
			record.put("probability",probability);
		});
		AtomicLong tem = new AtomicLong(0);
		records.forEach(record -> {
			long current = tem.get();
			record.put("start", current + 1);
			long count =  (Long) record.get("count");
			record.put("end", current + count);
			tem.addAndGet(count);
		});
		//开始抽奖，生成一个1到tem的随机整数
		long randomNum = (long)(Math.random() * tem.get()) + 1;
		for (Map<String, Object> record : records) {
			if (randomNum >= (Long) record.get("start") && randomNum <= (Long) record.get("end")) {
				return record;
			}
		}
		return null;
	}*/
	/**
	 * 抽奖
	 */
	@Override
	public Map<String, Object> getLottery(String startDate, String endDate) {
		//获取日期内所有签到记录
		List<Map<String, Object>> records = baseMapper.selectByDateRange(startDate, endDate);
		AtomicLong totalUser = new AtomicLong();
		AtomicLong totalSingins = new AtomicLong();

		records.forEach(record -> {
			totalUser.getAndIncrement();
			// 正确处理 Long 类型转换
			Long count = (Long) record.get("signin_count");
			totalSingins.addAndGet(count);
		});

		//根据总用户数和总签到数计算中奖概率，保证一定有一个人中奖，且签到次数多的人中奖率高
		records.forEach(record -> {
			// 使用 Number 类型安全转换
			Number countObj = (Number) record.get("signin_count");
			double probability = countObj.doubleValue() / totalSingins.get();
			record.put("probability", probability);
		});

		AtomicLong tem = new AtomicLong(0);
		records.forEach(record -> {
			long current = tem.get();
			record.put("start", current + 1);
			// 安全处理 Long 类型转换
			Number countObj = (Number) record.get("signin_count");
			long count = countObj.longValue();
			record.put("end", current + count);
			tem.addAndGet(count);
		});

		// 处理边界情况
		if (tem.get() <= 0) {
			return new HashMap<>(); // 返回空结果而不是null
		}

		//开始抽奖，生成一个1到tem的随机整数
/*		long randomNum = (long)(Math.random() * tem.get()) + 1;*/
		Random random = RandomUtil.getRandom();
		long randomNum = random.nextLong(tem.get())+1;
		for (Map<String, Object> record : records) {
			// 安全处理 Long 类型转换
			Number startObj = (Number) record.get("start");
			Number endObj = (Number) record.get("end");
			long start = startObj.longValue();
			long end = endObj.longValue();

			if (randomNum >= start && randomNum <= end) {
				redisTemplate.opsForValue().set("users:win", record.get("user_id"));
				//构造中奖记录
				Map<String, Object> winRecord = new HashMap<>();
				winRecord.put("user_id", record.get("user_id"));
				winRecord.put("participants",totalUser.get());
				winRecord.put("prize_rate", record.get("probability"));
				baseMapper.insertWinRecord(winRecord);
				return record;
			}
		}
		return new HashMap<>(); // 如果未找到匹配项，返回空Map
	}

	// 查看是否中奖,从Redis中获取
	@Override
	public Boolean IsWin() {
		String winId= (String)redisTemplate.opsForValue().get("users:win");
		if(winId != null){
			return AuthUtil.getUserId().toString().equals(winId);
		}
		throw new RuntimeException("没有抽奖活动");
	}


	//获取每个人的获奖概率
	@Override
	public List<Map<String, Object>> getWinRate(String startDate, String endDate) {
		//获取日期内所有签到记录
		List<Map<String, Object>> records = baseMapper.selectByDateRange(startDate, endDate);
		AtomicLong totalUser = new AtomicLong();
		AtomicLong totalSingins = new AtomicLong();
		records.forEach(record -> {
			totalUser.getAndIncrement();
			// 正确处理 Long 类型转换
			Long count = (Long) record.get("signin_count");
			totalSingins.addAndGet(count);
		});

		//根据总签到数计算中奖概率，保证一定有一个人中奖，且签到次数多的人中奖率高
		records.forEach(record -> {
			// 使用 Number 类型安全转换
			Number countObj = (Number) record.get("signin_count");
			double probability = countObj.doubleValue() / totalSingins.get();
			record.put("probability", probability);
		});
		return records;
	}

	@Override
	public Map<String, Object> getSigninRecords(String userId, Integer page, Integer size, String startDate, String endDate) {
		if (StrUtil.isBlank(userId) || page == null || size == null) {
			throw new RuntimeException("参数错误");
		}

		try {
			// 计算分页参数
			int offset = (page - 1) * size;

			// 查询总记录数
			Integer total = baseMapper.countUserRecords(userId, startDate, endDate);

			// 查询分页数据
			List<SigninRecord> records = baseMapper.selectUserRecords(userId, startDate, endDate);

			// 手动分页处理
			List<SigninRecord> pageRecords = records.stream()
				.skip(offset)
				.limit(size)
				.collect(java.util.stream.Collectors.toList());

			// 转换为返回格式
			List<Map<String, Object>> recordList = new ArrayList<>();
			for (SigninRecord record : pageRecords) {
				Map<String, Object> recordMap = new HashMap<>();
				recordMap.put("id", record.getId());
				recordMap.put("signinDate", record.getSigninDate().toString());
				recordMap.put("signinTime", record.getSigninTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
				recordMap.put("points", record.getPoints());
				recordMap.put("continuousReward", record.getContinuousReward());
				recordMap.put("continuousDays", record.getContinuousDays());
				recordMap.put("totalPoints", record.getPoints() + (record.getContinuousReward() != null ? record.getContinuousReward() : 0));
				recordMap.put("signinType", record.getSigninType());
				recordMap.put("signinTypeText", "NORMAL".equals(record.getSigninType()) ? "正常签到" : "补签");
				recordMap.put("weekDay", getWeekDayText(record.getWeekDay()));
				recordList.add(recordMap);
			}

			// 构建分页结果
			Map<String, Object> result = new HashMap<>();
			result.put("records", recordList);
			result.put("total", total);
			result.put("page", page);
			result.put("size", size);
			result.put("pages", (int) Math.ceil((double) total / size));
			result.put("hasNext", page * size < total);
			result.put("hasPrev", page > 1);

			return result;
		} catch (Exception e) {
			log.error("获取签到记录失败，userId: {}", userId, e);
			throw new RuntimeException("获取签到记录失败：" + e.getMessage());
		}
	}

	@Override
	public Map<String, Object> getSigninSummary(String userId) {
		if (StrUtil.isBlank(userId)) {
			throw new RuntimeException("用户ID不能为空");
		}

		try {
			Map<String, Object> summary = new HashMap<>();

			// 获取基础统计信息
			Map<String, Object> stats = getSigninStats(userId);
			summary.putAll(stats);

			// 获取总签到天数（所有时间）
			Integer totalAllDays = baseMapper.countSigninDays(userId);
			summary.put("totalAllDays", totalAllDays != null ? totalAllDays : 0);

			// 获取本月签到天数
			LocalDate now = LocalDate.now();
			Integer monthDays = baseMapper.countMonthSigninDays(userId, now.getYear(), now.getMonthValue());
			summary.put("monthDays", monthDays != null ? monthDays : 0);

			// 计算总获得积分
			List<SigninRecord> allRecords = baseMapper.selectUserRecords(userId, null, null);
			int totalPoints = allRecords.stream()
				.mapToInt(record -> record.getPoints() + (record.getContinuousReward() != null ? record.getContinuousReward() : 0))
				.sum();
			summary.put("totalPoints", totalPoints);

			// 计算平均每日积分
			double avgPoints = totalAllDays > 0 ? (double) totalPoints / totalAllDays : 0;
			summary.put("avgPoints", Math.round(avgPoints * 100.0) / 100.0);

			// 获取最长连续签到记录
			Integer maxContinuous = getMaxContinuousDays(userId);
			summary.put("maxContinuousDays", maxContinuous != null ? maxContinuous : 0);

			// 获取最近签到记录
			SigninRecord lastRecord = baseMapper.selectLastSignin(userId);
			if (lastRecord != null) {
				summary.put("lastSigninDate", lastRecord.getSigninDate().toString());
				summary.put("lastSigninTime", lastRecord.getSigninTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
			}

			return summary;
		} catch (Exception e) {
			log.error("获取签到统计汇总失败，userId: {}", userId, e);
			throw new RuntimeException("获取签到统计汇总失败：" + e.getMessage());
		}
	}

	/**
	 * 获取星期几的中文文本
	 */
	private String getWeekDayText(Integer weekDay) {
		if (weekDay == null) return "";
		String[] weekDays = {"", "周一", "周二", "周三", "周四", "周五", "周六", "周日"};
		return weekDay >= 1 && weekDay <= 7 ? weekDays[weekDay] : "";
	}

	/**
	 * 获取最长连续签到天数
	 */
	private Integer getMaxContinuousDays(String userId) {
		List<SigninRecord> records = baseMapper.selectUserRecords(userId, null, null);
		if (records.isEmpty()) {
			return 0;
		}

		// 按日期排序
		records.sort((a, b) -> a.getSigninDate().compareTo(b.getSigninDate()));

		int maxContinuous = 1;
		int currentContinuous = 1;

		for (int i = 1; i < records.size(); i++) {
			LocalDate prevDate = records.get(i - 1).getSigninDate();
			LocalDate currentDate = records.get(i).getSigninDate();

			// 如果是连续的日期
			if (currentDate.equals(prevDate.plusDays(1))) {
				currentContinuous++;
				maxContinuous = Math.max(maxContinuous, currentContinuous);
			} else {
				currentContinuous = 1;
			}
		}

		return maxContinuous;
	}

}
