package org.springblade.miniapp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.business.group.entity.GroupCategory;
import org.springblade.business.group.entity.GroupInfo;
import org.springblade.business.group.vo.GroupCategoryVO;
import org.springblade.business.group.vo.GroupInfoVO;

/**
 * <AUTHOR>
 * @version 1.0
 * @email <EMAIL>
 * @website duofan.top
 * @date 2025/7/26
 */
public interface WechatGroupService {
    IPage<GroupCategoryVO> selectGroupCategoryPage(IPage<GroupCategoryVO> page, GroupCategoryVO groupCategory);

    IPage<GroupInfoVO> selectGroupInfoPage(IPage<GroupInfoVO> page, GroupInfoVO groupInfo);
}
