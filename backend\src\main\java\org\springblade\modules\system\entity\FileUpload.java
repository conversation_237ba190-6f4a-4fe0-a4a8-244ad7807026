/**
 * Copyright (c) 2018-2099, Chill <PERSON><PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.modules.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

import java.io.Serial;

/**
 * 文件上传实体类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Data
@TableName("blade_file_upload")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "文件上传")
public class FileUpload extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 原始文件名
     */
    @Schema(description = "原始文件名")
    private String originalName;

    /**
     * 存储文件名
     */
    @Schema(description = "存储文件名")
    private String fileName;

    /**
     * 文件扩展名
     */
    @Schema(description = "文件扩展名")
    private String fileExtension;

    /**
     * 文件大小（字节）
     */
    @Schema(description = "文件大小（字节）")
    private Long fileSize;

    /**
     * 文件类型（MIME类型）
     */
    @Schema(description = "文件类型（MIME类型）")
    private String contentType;

    /**
     * 文件分类
     */
    @Schema(description = "文件分类")
    private String fileCategory;

    /**
     * 上传来源
     */
    @Schema(description = "上传来源")
    private String uploadSource;

    /**
     * 云存储提供商
     */
    @Schema(description = "云存储提供商")
    private String storageProvider;

    /**
     * 存储桶名称
     */
    @Schema(description = "存储桶名称")
    private String bucketName;

    /**
     * 存储路径
     */
    @Schema(description = "存储路径")
    private String storagePath;

    /**
     * 访问URL
     */
    @Schema(description = "访问URL")
    private String accessUrl;

    /**
     * 缩略图URL
     */
    @Schema(description = "缩略图URL")
    private String thumbnailUrl;

    /**
     * 文件MD5值
     */
    @Schema(description = "文件MD5值")
    private String fileMd5;

    /**
     * 文件状态：0-上传中，1-上传成功，2-上传失败
     */
    @Schema(description = "文件状态")
    private Integer status;

    /**
     * 关联业务ID
     */
    @Schema(description = "关联业务ID")
    private Long businessId;

    /**
     * 关联业务类型
     */
    @Schema(description = "关联业务类型")
    private String businessType;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息")
    private String remark;
} 