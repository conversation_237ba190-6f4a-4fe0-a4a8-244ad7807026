<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.post.mapper.DataRecordMapper">

    <!-- 分页查询浏览记录 -->
    <select id="selectViewRecords" resultType="java.util.Map">
        SELECT
            vl.id,
            vl.user_id as userId,
            u.nickname as userNickname,
            u.avatar as userAvatar,
            vl.ip,
            vl.view_time as operateTime
        FROM urb_view_log vl
        LEFT JOIN urb_user u ON vl.user_id = u.id
        WHERE vl.relevancy_id = #{relevancyId}
        AND vl.type = #{type}
        AND vl.is_deleted = 0
        ORDER BY vl.view_time DESC
    </select>

    <!-- 分页查询点赞记录 -->
    <select id="selectLikeRecords" resultType="java.util.Map">
        SELECT
            ll.id,
            ll.user_id as userId,
            u.nickname as userNickname,
            u.avatar as userAvatar,
            ll.ip,
            ll.like_time as operateTime
        FROM urb_like ll
        LEFT JOIN urb_user u ON ll.user_id = u.id
        WHERE ll.relevancy_id = #{relevancyId}
        AND ll.type = #{type}
        AND ll.is_deleted = 0
        ORDER BY ll.like_time DESC
    </select>

    <!-- 分页查询反馈记录 -->
    <select id="selectFeedbackRecords" resultType="java.util.Map">
        SELECT
            fl.id,
            fl.user_id as userId,
            u.nickname as userNickname,
            u.avatar as userAvatar,
            fl.content,
            fl.star,
            fl.comment_type as commentType,
            fl.ip,
            fl.create_time as operateTime
        FROM urb_feedback fl
        LEFT JOIN urb_user u ON fl.user_id = u.id
        WHERE fl.relevancy_id = #{relevancyId}
        AND fl.is_deleted = 0
        ORDER BY fl.create_time DESC
    </select>

    <!-- 分页查询收藏记录 -->
    <select id="selectFavoriteRecords" resultType="java.util.Map">
        SELECT
            fv.id,
            fv.user_id as userId,
            u.nickname as userNickname,
            u.avatar as userAvatar,
            fv.fav_time as operateTime
        FROM urb_favorite fv
        LEFT JOIN urb_user u ON fv.user_id = u.id
        WHERE fv.relevancy_id = #{relevancyId}
        AND fv.type = #{type}
        AND fv.is_deleted = '0'
        ORDER BY fv.fav_time DESC
    </select>

</mapper>
