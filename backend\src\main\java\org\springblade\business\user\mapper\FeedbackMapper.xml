<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.user.mapper.FeedbackMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="feedbackResultMap" type="org.springblade.business.post.vo.FeedbackVO">
        <result column="id" property="id"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="reason" property="reason"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="relevancy_id" property="relevancyId"/>
        <result column="user_id" property="userId"/>
        <result column="parent_id" property="parentId"/>
        <result column="reply_to_user_id" property="replyToUserId"/>
        <result column="reply_to_user_name" property="replyToUserName"/>
        <result column="comment_type" property="commentType"/>
        <result column="level" property="level"/>
        <result column="reply_count" property="replyCount"/>
        <result column="content" property="content"/>
        <result column="image" property="image"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="is_helpful" property="helpful"/>
        <result column="helpful_count" property="helpfulCount"/>
        <result column="nickname" property="nickname"/>
        <result column="avatar" property="avatar"/>
        <result column="is_author" property="isAuthor"/>
        <!-- 帖子信息映射 -->
        <association property="post" javaType="org.springblade.business.post.entity.SupPost">
            <result column="post_id" property="id"/>
            <result column="post_title" property="title"/>
            <result column="post_content" property="content"/>
            <result column="post_images" property="images"/>
            <result column="post_address" property="address"/>
            <result column="post_publish_time" property="publishTime"/>
            <result column="post_publish_status" property="publishStatus"/>
            <result column="post_audit_status" property="auditStatus"/>
            <result column="post_contact_name" property="contactName"/>
            <result column="post_contact_phone" property="contactPhone"/>
            <result column="post_category_id" property="categoryId"/>
            <result column="post_create_time" property="createTime"/>
        </association>

<!--        机构信息-->
        <association property="institution" javaType="org.springblade.business.institution.entity.Institution">
            <result column="institution_id" property="id"/>
            <result column="institution_create_dept" property="createDept"/>
            <result column="institution_create_time" property="createTime"/>
            <result column="institution_create_user" property="createUser"/>
            <result column="institution_is_deleted" property="isDeleted"/>
            <result column="institution_status" property="status"/>
            <result column="institution_update_time" property="updateTime"/>
            <result column="institution_update_user" property="updateUser"/>
            <result column="institution_name" property="name"/>
            <result column="institution_logo" property="logo"/>
            <result column="institution_type_id" property="typeId"/>
            <result column="institution_years_in_business" property="yearsInBusiness"/>
            <result column="institution_description" property="description"/>
            <result column="institution_contact_person" property="contactPerson"/>
            <result column="institution_phone" property="phone"/>
            <result column="institution_backup_phone" property="backupPhone"/>
            <result column="institution_email" property="email"/>
            <result column="institution_wechat" property="wechat"/>
            <result column="institution_qq" property="qq"/>
            <result column="institution_license_no" property="licenseNo"/>
            <result column="institution_license_image" property="licenseImage"/>
            <result column="institution_legal_person" property="legalPerson"/>
            <result column="institution_industry_license" property="industryLicense"/>
            <result column="institution_tax_certificate" property="taxCertificate"/>
            <result column="institution_province" property="province"/>
            <result column="institution_city" property="city"/>
            <result column="institution_district" property="district"/>
            <result column="institution_detail_address" property="detailAddress"/>
            <result column="institution_longitude" property="longitude"/>
            <result column="institution_latitude" property="latitude"/>
            <result column="institution_is_store" property="isStore"/>
            <result column="institution_service_radius" property="serviceRadius"/>
            <result column="institution_has_delivery" property="hasDelivery"/>
            <result column="institution_payment_methods" property="paymentMethods"/>
            <result column="institution_special_services" property="specialServices"/>
            <result column="institution_username" property="username"/>
            <result column="institution_password" property="password"/>
            <result column="institution_salt" property="salt"/>
            <result column="institution_last_login_time" property="lastLoginTime"/>
            <result column="institution_last_login_ip" property="lastLoginIp"/>
            <result column="institution_is_locked" property="isLocked"/>
            <result column="institution_business_hours" property="businessHours"/>
            <result column="institution_images" property="images"/>
            <result column="institution_audit_status" property="auditStatus"/>
            <result column="institution_audit_remark" property="auditRemark"/>
            <result column="institution_apply_user_id" property="applyUserId"/>
            <result column="institution_apply_time" property="applyTime"/>
            <result column="institution_last_audit_time" property="lastAuditTime"/>
            <result column="institution_last_audit_user_id" property="lastAuditUserId"/>
            <result column="institution_top" property="top"/>
            <result column="institution_region_code" property="regionCode"/>
        </association>

    </resultMap>
    <insert id="InsertFeedbackHelpful">
        INSERT INTO urb_feedback_helpful (user_id, feedback_id, create_time)
        VALUES (#{model.userId}, #{model.feedbackId}, NOW());
    </insert>
    <insert id="addTag">
        insert into urb_feedback_tag (label,description,sort_order,category_id)
        values (#{model.label}, #{model.description}, #{model.sortOrder}, #{model.categoryId});
    </insert>


    <select id="selectFeedbackPage" resultMap="feedbackResultMap">
        select f.id,
        f.create_dept,
        f.create_time,
        f.create_user,
        f.is_deleted,
        f.status,
        f.reason,
        f.update_time,
        f.update_user,
        f.relevancy_id,
        f.user_id,
        f.parent_id,
        f.reply_to_user_id,
        f.reply_to_user_name,
        f.comment_type,
        f.level,
        f.content,
        f.image,
        f.audit_status,
        f.is_author,
        u.nickname,
        u.avatar,
        <!-- 返回自己是否标记有帮助 -->
        CASE
            WHEN #{model.userId} IS NOT NULL AND EXISTS (
                SELECT 1
                FROM urb_comment_like
                WHERE comment_id = f.id
                    AND user_id = #{model.userId}
            ) THEN 1 ELSE 0
        END AS is_helpful,
        <!-- 返回总标记数 -->
        (select count(*) from urb_comment_like where comment_id = f.id) as helpful_count,
        <!-- 动态计算回复数量 -->
        (select count(*) from urb_feedback where parent_id = f.id and is_deleted = 0) as reply_count,
        <!-- 帖子信息 -->
        p.id as ppost_id,
        p.title as post_title,
        p.content as post_content,
        p.images as post_images,
        p.address as post_address,
        p.publish_time as post_publish_time,
        p.publish_status as post_publish_status,
        p.audit_status as post_audit_status,
        p.contact_name as post_contact_name,
        p.contact_phone as post_contact_phone,
        p.category_id as post_category_id,
        p.create_time as post_create_time,

        <!-- 机构信息 -->
        i.id as institution_id,
        i.create_dept as institution_create_dept,
        i.create_time as institution_create_time,
        i.create_user as institution_create_user,
        i.is_deleted as institution_is_deleted,
        i.status as institution_status,
        i.update_time as institution_update_time,
        i.update_user as institution_update_user,
        i.name as institution_name,
        i.logo as institution_logo,
        i.type_id as institution_type_id,
        i.years_in_business as institution_years_in_business,
        i.description as institution_description,
        i.contact_person as institution_contact_person,
        i.phone as institution_phone,
        i.backup_phone as institution_backup_phone,
        i.email as institution_email,
        i.wechat as institution_wechat,
        i.qq as institution_qq,
        i.license_no as institution_license_no,
        i.license_image as institution_license_image,
        i.legal_person as institution_legal_person,
        i.industry_license as institution_industry_license,
        i.tax_certificate as institution_tax_certificate,
        i.province as institution_province,
        i.city as institution_city,
        i.district as institution_district,
        i.detail_address as institution_detail_address,
        i.longitude as institution_longitude,
        i.latitude as institution_latitude,
        i.is_store as institution_is_store,
        i.service_radius as institution_service_radius,
        i.has_delivery as institution_has_delivery,
        i.payment_methods as institution_payment_methods,
        i.special_services as institution_special_services,
        i.username as institution_username,
        i.password as institution_password,
        i.salt as institution_salt,
        i.last_login_time as institution_last_login_time,
        i.last_login_ip as institution_last_login_ip,
        i.is_locked as institution_is_locked,
        i.business_hours as institution_business_hours,
        i.images as institution_images,
        i.audit_status as institution_audit_status,
        i.audit_remark as institution_audit_remark,
        i.apply_user_id as institution_apply_user_id,
        i.apply_time as institution_apply_time,
        i.last_audit_time as institution_last_audit_time,
        i.last_audit_user_id as institution_last_audit_user_id,
        i.top as institution_top,
        i.region_code as institution_region_code
        from urb_feedback f
        LEFT JOIN urb_user u ON f.create_user = u.id
        LEFT JOIN urb_post p ON f.relevancy_id = p.id AND f.type = '0'
        LEFT JOIN urb_institution i ON f.relevancy_id = i.id AND f.type = '1'
        where f.is_deleted = 0
        <if test="model.relevancyId != null">
            and f.relevancy_id = #{model.relevancyId}
        </if>
        <if test="model.auditStatus != null">
            and f.audit_status = #{model.auditStatus}
        </if>
        <if test="model.nickname!=null">
            and u.nickname like concat('%', #{model.nickname}, '%')
        </if>
        <if test="model.parentId == null">
            and f.parent_id IS NULL
        </if>
        <if test="model.parentId != null">
            and f.parent_id = #{model.parentId}
        </if>
        <if test="model.commentType != null">
            and f.comment_type = #{model.commentType}
        </if>
        order by helpful_count desc, f.create_time desc
    </select>
    <select id="getTagsByCategory" resultType="java.util.Map">
        select id, label, description, sort_order, category_id, status
        from urb_feedback_tag
        where category_id = #{categoryId} and status = 1
        order by sort_order asc
    </select>
    <select id="getHotTags" resultType="java.lang.String">
        select urb_feedback_tag.label from urb_feedback
        left join urb_feedback_tag on urb_feedback.reason = urb_feedback_tag.label
        group by urb_feedback_tag.label
        order by count(*) desc
        limit 5
    </select>

    <!-- 移除反馈标签 -->
    <delete id="removeTag">
        DELETE FROM urb_feedback_tag
        WHERE category_id = #{categoryId} AND id = #{tagId}
    </delete>

    <!-- 获取所有反馈标签 -->
    <select id="getAllFeedbackTags" resultType="java.util.Map">
        SELECT id, label, description, sort_order, category_id, status
        FROM urb_feedback_tag
        WHERE status = 1
        ORDER BY sort_order ASC, id ASC
    </select>

    <!-- 获取反馈详情（包含帖子信息） -->
    <select id="getFeedbackDetail" resultMap="feedbackResultMap">
        select f.*,
        u.nickname,
        u.avatar,
        <!-- 返回自己是否标记有帮助 -->
        CASE
            WHEN EXISTS (
                SELECT 1
                FROM urb_feedback_helpful
                WHERE feedback_id = f.id
                    AND user_id = #{model.userId}
            ) THEN 1 ELSE 0
        END AS is_helpful,
        <!-- 返回总标记数 -->
        (select count(*) from urb_feedback_helpful where feedback_id = f.id) as helpful_count,
        <!-- 帖子信息 -->
        p.id as ppost_id,
        p.title as post_title,
        p.content as post_content,
        p.images as post_images,
        p.address as post_address,
        p.publish_time as post_publish_time,
        p.publish_status as post_publish_status,
        p.audit_status as post_audit_status,
        p.contact_name as post_contact_name,
        p.contact_phone as post_contact_phone,
        p.category_id as post_category_id,
        p.create_time as post_create_time,

        <!-- 机构信息 -->
        i.id as institution_id,
        i.create_dept as institution_create_dept,
        i.create_time as institution_create_time,
        i.create_user as institution_create_user,
        i.is_deleted as institution_is_deleted,
        i.status as institution_status,
        i.update_time as institution_update_time,
        i.update_user as institution_update_user,
        i.name as institution_name,
        i.logo as institution_logo,
        i.type_id as institution_type_id,
        i.years_in_business as institution_years_in_business,
        i.description as institution_description,
        i.contact_person as institution_contact_person,
        i.phone as institution_phone,
        i.backup_phone as institution_backup_phone,
        i.email as institution_email,
        i.wechat as institution_wechat,
        i.qq as institution_qq,
        i.license_no as institution_license_no,
        i.license_image as institution_license_image,
        i.legal_person as institution_legal_person,
        i.industry_license as institution_industry_license,
        i.tax_certificate as institution_tax_certificate,
        i.province as institution_province,
        i.city as institution_city,
        i.district as institution_district,
        i.detail_address as institution_detail_address,
        i.longitude as institution_longitude,
        i.latitude as institution_latitude,
        i.is_store as institution_is_store,
        i.service_radius as institution_service_radius,
        i.has_delivery as institution_has_delivery,
        i.payment_methods as institution_payment_methods,
        i.special_services as institution_special_services,
        i.username as institution_username,
        i.password as institution_password,
        i.salt as institution_salt,
        i.last_login_time as institution_last_login_time,
        i.last_login_ip as institution_last_login_ip,
        i.is_locked as institution_is_locked,
        i.business_hours as institution_business_hours,
        i.images as institution_images,
        i.audit_status as institution_audit_status,
        i.audit_remark as institution_audit_remark,
        i.apply_user_id as institution_apply_user_id,
        i.apply_time as institution_apply_time,
        i.last_audit_time as institution_last_audit_time,
        i.last_audit_user_id as institution_last_audit_user_id,
        i.top as institution_top,
        i.region_code as institution_region_code
         from urb_feedback f
        LEFT JOIN urb_user u ON f.create_user = u.id
        LEFT JOIN urb_post p ON f.relevancy_id = p.id
        LEFT JOIN urb_institution i ON f.relevancy_id = i.id
        where f.is_deleted = 0 AND
        <if test="model.id != null">
            and f.id = #{model.id}
        </if>
        <if test="model.relevancyId != null">
            and f.relevancy_id = #{model.relevancyId}
        </if>
        <if test="model.userId != null">
            and f.user_id = #{model.userId}
        </if>
        <if test="model.type != null">
            and f.type = #{model.type}
        </if>
        limit 1
    </select>
    <select id="getFeedbackCountByUserId" resultType="java.lang.Long">
            select count(*) from urb_feedback where user_id = #{userId} and is_deleted = 0
    </select>
    <select id="selectFeedbackPageByUserId" resultMap="feedbackResultMap">
            select f.*,
            u.nickname,
            u.avatar,
            <!-- 帖子信息 -->
            p.id as post_id,
            p.title as post_title,
            p.content as post_content,
            p.images as post_images,
            p.address as post_address,
            p.publish_time as post_publish_time,
            p.publish_status as post_publish_status,
            p.audit_status as post_audit_status,
            p.contact_name as post_contact_name,
            p.contact_phone as post_contact_phone,
            p.category_id as post_category_id,
            p.create_time as post_create_time
              from urb_feedback f
            LEFT JOIN urb_user u ON f.create_user = u.id
            LEFT JOIN urb_post p ON f.relevancy_id = p.id
            where f.is_deleted = 0 and f.user_id = #{userId}
    </select>

    <!-- 插入评论点赞记录 -->
    <insert id="insertCommentLike">
        INSERT INTO urb_comment_like (comment_id, user_id, like_type, create_time)
        VALUES (#{model.commentId}, #{model.userId}, #{model.likeType}, NOW())
    </insert>

    <!-- 删除评论点赞记录 -->
    <delete id="deleteCommentLike">
        DELETE FROM urb_comment_like
        WHERE comment_id = #{model.commentId}
        AND user_id = #{model.userId}
        AND like_type = #{model.likeType}
    </delete>

</mapper>
