/**
 * Copyright (c) 2018-2099, Chill <PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.modules.system.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 云存储配置
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Data
@Component
@ConfigurationProperties(prefix = "blade.storage")
public class CloudStorageConfig {

    /**
     * 存储提供商：aliyun-oss, cloudflare
     */
    private String provider = "aliyun-oss";

    /**
     * 阿里云OSS配置
     */
    private AliyunOssConfig aliyunOss = new AliyunOssConfig();

    /**
     * Cloudflare配置
     */
    private CloudflareConfig cloudflare = new CloudflareConfig();

    /**
     * 文件上传配置
     */
    private UploadConfig upload = new UploadConfig();

    @Data
    public static class AliyunOssConfig {
        /**
         * 访问密钥ID
         */
        private String accessKeyId;

        /**
         * 访问密钥Secret
         */
        private String accessKeySecret;

        /**
         * 端点
         */
        private String endpoint;

        /**
         * 存储桶名称
         */
        private String bucketName;

        /**
         * 域名
         */
        private String domain;

        /**
         * 区域
         */
        private String region;
    }

    @Data
    public static class CloudflareConfig {
        /**
         * 账户ID
         */
        private String accountId;

        /**
         * API Token
         */
        private String apiToken;

        /**
         * 存储桶名称
         */
        private String bucketName;

        /**
         * 域名
         */
        private String domain;
    }

    @Data
    public static class UploadConfig {
        /**
         * 最大文件大小（MB）
         */
        private Long maxFileSize = 10L;

        /**
         * 允许的文件类型
         */
        private String[] allowedTypes = {
            "image/jpeg", "image/png", "image/gif", "image/webp",
            "application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "text/plain", "text/csv"
        };

        /**
         * 存储路径前缀
         */
        private String pathPrefix = "uploads";

        /**
         * 是否生成缩略图
         */
        private Boolean generateThumbnail = true;

        /**
         * 缩略图尺寸
         */
        private Integer thumbnailWidth = 200;
        private Integer thumbnailHeight = 200;
    }
}
