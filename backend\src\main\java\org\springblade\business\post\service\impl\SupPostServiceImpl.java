/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<PERSON><PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springblade.business.post.dto.PostAuditDTO;
import org.springblade.business.post.entity.AuditPost;
import org.springblade.business.post.entity.SupPost;
import org.springblade.business.post.service.ICategoryService;
import org.springblade.business.post.service.ISupPostService;
import org.springblade.business.post.service.ITagService;
import org.springblade.business.post.vo.SupPostVO;
import org.springblade.business.user.mapper.SupPostMapper;
import org.springblade.common.constant.bizz.AuditStatusEnum;
import org.springblade.common.constant.bizz.PostBusinessType;
import org.springblade.common.constant.bizz.PublishStatusEnum;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 百事通信息贴 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Service
@AllArgsConstructor
public class SupPostServiceImpl extends BaseServiceImpl<SupPostMapper, SupPost> implements ISupPostService {

	private final ICategoryService categoryService;
	private final ITagService tagService;

	@Override
	public IPage<SupPostVO> selectPostPage(IPage<SupPostVO> page, SupPostVO post) {
		List<OrderItem> orders = page.orders();
//		TODO 提取一个工具类
		for (OrderItem orderItem : orders){
			if (orderItem.getColumn().equals("viewCount")){
				orderItem.setColumn("view_count");
			}
			if (orderItem.getColumn().equals("favoriteCount")){
				orderItem.setColumn("favorite_count");
			}
			if (orderItem.getColumn().equals("likeCount")){
				orderItem.setColumn("like_count");
			}
		}
		//拿到accsArray,把page转化为query
		List<SupPostVO> supPostVOS = baseMapper.selectPostPage(page, post);
		return page.setRecords(supPostVOS);
	}


	@Override
	public IPage<SupPostVO> getMyPosts(Integer current, Integer size) {
		Page<SupPost> page = new Page<>(current, size);
		LambdaQueryWrapper<SupPost> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(SupPost::getCreateUser, AuthUtil.getUserId())
			.orderByDesc(SupPost::getCreateTime);

		IPage<SupPost> postPage = page(page, queryWrapper);
		return convertToVO(page, postPage.getRecords());
	}

	@Override
	public IPage<SupPostVO> getPostList(Long categoryId, String keyword, Integer current, Integer size) {
		Page<SupPost> page = new Page<>(current, size);
		LambdaQueryWrapper<SupPost> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(SupPost::getAuditStatus, "APPROVED")
			.eq(SupPost::getPublishStatus, PublishStatusEnum.PUBLISHED.getValue()) // 只查询上架的帖子
			.eq(categoryId != null, SupPost::getCategoryId, categoryId)
			.like(StrUtil.isNotBlank(keyword), SupPost::getTitle, keyword)
			.or()
			.like(StrUtil.isNotBlank(keyword), SupPost::getContent, keyword)
			.orderByDesc(SupPost::getTop)
			.orderByDesc(SupPost::getCreateTime);

		IPage<SupPost> postPage = page(page, queryWrapper);
		return convertToVO(page, postPage.getRecords());
	}

	@Override
	public Boolean likePost(Long id, String openId) {
		// TODO: 实现点赞逻辑，需要创建点赞表
		return true;
	}

	@Override
	public Boolean favoritePost(Long id, String openId) {
		// TODO: 实现收藏逻辑，需要创建收藏表
		return true;
	}

	@Override
	public IPage<SupPostVO> getFavoritePosts(String openId, Integer current, Integer size) {
		// TODO: 实现获取收藏帖子逻辑
		return new Page<>();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean auditPost(PostAuditDTO postAuditDTO) {
		SupPost post = new SupPost();
		post.setAuditStatus(postAuditDTO.getAuditStatus());
		post.setAuditRemark(postAuditDTO.getAuditRemark());
		post.setAuditTime(LocalDateTime.now());
		post.setAuditUserId(AuthUtil.getUserId());
		if (postAuditDTO.getAuditStatus().equals(AuditStatusEnum.APPROVED.getCode())){
			post.setPublishStatus(PublishStatusEnum.PUBLISHED.getValue());
		}

		AuditPost audit = new AuditPost();
		audit.setPostId(post.getId());
		audit.setAuditRemark(postAuditDTO.getAuditRemark());
		audit.setAuditUser(AuthUtil.getUserId());
		audit.setAuditTime(LocalDateTime.now());
		audit.setAuditStatus(postAuditDTO.getAuditStatus());
		this.baseMapper.addAuditRecord(audit);

		return updateById(post);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean batchAuditPosts(List<Long> postIds, String auditStatus, String auditRemark) {
		for (Long postId : postIds) {
			//修改帖子审核状态
			SupPost post = new SupPost();
			post.setId(postId);
			post.setAuditStatus(auditStatus);
			post.setAuditRemark(auditRemark);
			updateById(post);

			//增加审核日志
			PostAuditDTO auditDTO = new PostAuditDTO();
			auditDTO.setPostId(postId);
			auditDTO.setAuditStatus(auditStatus);
			auditDTO.setAuditRemark(auditRemark);
			auditPost(auditDTO);
		}
		return true;
	}


	@Override
	public Boolean topPost(Long id, Boolean isTop) {
		SupPost post = getById(id);
		if (post == null) {
			return false;
		}
		post.setTop(isTop ? "1" : "0");
		return updateById(post);
	}

	@Override
	public Map<String, Object> getAuditStats() {
		Map<String, Object> stats = new HashMap<>();

		// 待审核数量
		long pendingCount = count(new LambdaQueryWrapper<SupPost>()
			.eq(SupPost::getAuditStatus, AuditStatusEnum.PENDING.getCode()));
		stats.put("pendingCount", pendingCount);

		// 已通过数量
		long approvedCount = count(new LambdaQueryWrapper<SupPost>()
			.eq(SupPost::getAuditStatus,  AuditStatusEnum.APPROVED.getCode()));
		stats.put("approvedCount", approvedCount);

		// 已拒绝数量
		long rejectedCount = count(new LambdaQueryWrapper<SupPost>()
			.eq(SupPost::getAuditStatus,  AuditStatusEnum.REJECTED.getCode()));
		stats.put("rejectedCount", rejectedCount);

		return stats;
	}

	@Override
	public Map<String, Object> getPostStats() {
		Map<String, Object> stats = new HashMap<>();

		// 总帖子数
		long totalCount = count();
		stats.put("totalCount", totalCount);

		// 今日发布数
		long todayCount = count(new LambdaQueryWrapper<SupPost>()
			.ge(SupPost::getCreateTime, LocalDateTime.now().withHour(0).withMinute(0).withSecond(0)));
		stats.put("todayCount", todayCount);

		// 本周发布数
		long weekCount = count(new LambdaQueryWrapper<SupPost>()
			.ge(SupPost::getCreateTime, LocalDateTime.now().minusWeeks(1)));
		stats.put("weekCount", weekCount);

		return stats;
	}

	@Override
	public Long getPostCountByUserId(Long id) {
		LambdaQueryWrapper<SupPost> wp = new LambdaQueryWrapper<>();
		wp.eq(SupPost::getCreateUser,id);
		return this.baseMapper.selectCount(wp);
	}

	@Override
	public IPage<SupPostVO> getPostListByUserId(Long userId, Query query) {
		Map<String, Object> params = new HashMap<>();
		params.put("userId", userId);
		return this.baseMapper.selectPostList(Condition.getPage(query), params);
	}

	@Override
	public SupPostVO getPostDetail(Long id) {
		// TODO 支持查询详情  封装外键查询内容
		// this.baseMapper.selectPostDetail(id);
		return null;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean remove(List<Long> longList) {
		// TODO 删除帖子 支持删除管理的信息 封装外键删除内容
		longList.forEach(this::remove);
		return true;
	}

	@Override
	public Long getPostCountByUserIdAndDate(Long userId, LocalDate start, LocalDate end) {
		return baseMapper.getPostCountByUserIdAndDate(userId, start, end);
	}

	@Transactional(rollbackFor = Exception.class)
	public boolean remove(Long id) {
		// TODO 删除帖子 支持删除管理的信息 封装外键删除内容
		SupPostVO postDetail = this.getPostDetail(id);
		String businessType = null;
		if (postDetail != null && postDetail.getBusinessType() != null) {
			businessType = postDetail.getBusinessType();
			if (businessType.equals(PostBusinessType.CARPOOL.getValue())) {

			}
		}

		this.removeById(id);
		return true;
	}

	/**
	 * 转换为VO
	 */
	private SupPostVO convertToVO(SupPost post) {
		SupPostVO vo = new SupPostVO();
		BeanUtil.copyProperties(post, vo);
		return vo;
	}

	/**
	 * 转换为VO列表
	 */
	private IPage<SupPostVO> convertToVO(Page<SupPost> page, List<SupPost> posts) {
		Page<SupPostVO> voPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
		//补充record属性
		List<SupPostVO> voList = posts.stream()
			.map(this::convertToVO)
			.collect(Collectors.toList());
		voPage.setRecords(voList);
		return voPage;
	}

	@Override
	public Boolean updatePublishStatus(Long id, String publishStatus) {
		if (id == null || publishStatus == null) {
			return false;
		}

		// 验证发布状态值
		if (!PublishStatusEnum.OFFLINE.getValue().equals(publishStatus) && !PublishStatusEnum.PUBLISHED.getValue().equals(publishStatus)) {
			throw new ServiceException("发布状态值无效，只能是2（下架）或1（上架）");
		}

		SupPost post = new SupPost();
		post.setId(id);
		post.setPublishStatus(publishStatus);

		return this.updateById(post);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean batchUpdatePublishStatus(List<Long> postIds, String publishStatus) {
		if (postIds == null || postIds.isEmpty() || publishStatus == null) {
			return false;
		}

		// 验证发布状态值
		if (!PublishStatusEnum.OFFLINE.getValue().equals(publishStatus) && !PublishStatusEnum.PUBLISHED.getValue().equals(publishStatus)) {
			throw new ServiceException("发布状态值无效，只能是2（下架）或1（上架）");
		}

		// 批量更新
		LambdaUpdateWrapper<SupPost> updateWrapper = new LambdaUpdateWrapper<>();
		updateWrapper.in(SupPost::getId, postIds)
			.set(SupPost::getPublishStatus, publishStatus)
			.set(SupPost::getUpdateTime, LocalDateTime.now())
			.set(SupPost::getUpdateUser, AuthUtil.getUserId());

		return this.update(updateWrapper);
	}


}
