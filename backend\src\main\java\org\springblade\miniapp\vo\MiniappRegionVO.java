/**
 * Copyright (c) 2018-2099, Chill <PERSON><PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.miniapp.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 小程序地区视图对象
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "小程序地区视图对象")
public class MiniappRegionVO implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@Schema(description = "主键")
	private Long id;

	/**
	 * 地区编码
	 */
	@Schema(description = "地区编码")
	private String regionCode;

	/**
	 * 地区名称
	 */
	@Schema(description = "地区名称")
	private String regionName;

	/**
	 * 父级地区编码
	 */
	@Schema(description = "父级地区编码")
	private String parentCode;

	/**
	 * 地区层级
	 */
	@Schema(description = "地区层级")
	private Integer level;

	/**
	 * 排序
	 */
	@Schema(description = "排序")
	private Integer sort;

	/**
	 * 省份编码
	 */
	@Schema(description = "省份编码")
	private String provinceCode;

	/**
	 * 省份名称
	 */
	@Schema(description = "省份名称")
	private String provinceName;

	/**
	 * 城市编码
	 */
	@Schema(description = "城市编码")
	private String cityCode;

	/**
	 * 城市名称
	 */
	@Schema(description = "城市名称")
	private String cityName;

	/**
	 * 区县编码
	 */
	@Schema(description = "区县编码")
	private String districtCode;

	/**
	 * 区县名称
	 */
	@Schema(description = "区县名称")
	private String districtName;

	/**
	 * 乡镇编码
	 */
	@Schema(description = "乡镇编码")
	private String townCode;

	/**
	 * 乡镇名称
	 */
	@Schema(description = "乡镇名称")
	private String townName;

	/**
	 * 村编码
	 */
	@Schema(description = "村编码")
	private String villageCode;

	/**
	 * 村名称
	 */
	@Schema(description = "村名称")
	private String villageName;

	/**
	 * 完整地址
	 */
	@Schema(description = "完整地址")
	private String fullAddress;

	/**
	 * 是否有子级
	 */
	@Schema(description = "是否有子级")
	private Boolean hasChildren;

}
