package org.springblade.miniapp.service;

/**
 * 数据操作服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @email <EMAIL>
 * @website duofan.top
 * @date 2025/7/31
 */
public interface WeChatDataOperateService {

	/**
	 * 切换点赞状态
	 * @param id 目标ID
	 * @param type 类型
	 * @return 操作后的状态：true-已点赞，false-已取消点赞
	 */
	boolean toggleLike(Long id, String type);

	/**
	 * 切换收藏状态
	 * @param id 目标ID
	 * @param type 类型
	 * @return 操作后的状态：true-已收藏，false-已取消收藏
	 */
	boolean toggleFavorite(Long id, String type);

	/**
	 * 记录分享行为
	 * @param id 目标ID
	 * @param type 类型
	 * @return 是否成功
	 */
	boolean share(Long id, String type);

	/**
	 * 记录浏览行为
	 * @param id 目标ID
	 * @param type 类型
	 * @return 是否成功
	 */
	boolean view(Long id, String type);

	/**
	 * 检查用户是否已点赞
	 * @param id 目标ID
	 * @param type 类型
	 * @return 是否已点赞
	 */
	boolean isLiked(Long id, String type);

	/**
	 * 检查用户是否已收藏
	 * @param id 目标ID
	 * @param type 类型
	 * @return 是否已收藏
	 */
	boolean isFavorited(Long id, String type);

	// 保持向后兼容的方法
	@Deprecated
	default boolean like(Long id, String type) {
		return toggleLike(id, type);
	}

	@Deprecated
	default boolean favorite(Long id, String type) {
		return toggleFavorite(id, type);
	}
}
