package org.springblade.fileShare.wrapper;

import java.util.Objects;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.fileShare.entity.FileShare;
import org.springblade.fileShare.vo.FileShareVO;

/**
 * 文件分享包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public class FileShareWrapper extends BaseEntityWrapper<FileShare, FileShareVO> {

    @Override
    public FileShareVO entityVO(FileShare fileShare) {
        FileShareVO fileShareVO = Objects.requireNonNull(BeanUtil.copy(fileShare, FileShareVO.class));

        return fileShareVO;
    }
}
