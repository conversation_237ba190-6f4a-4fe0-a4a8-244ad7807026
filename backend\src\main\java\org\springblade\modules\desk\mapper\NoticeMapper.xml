<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.desk.mapper.NoticeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="noticeResultMap" type="org.springblade.modules.desk.entity.Notice">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="release_time" property="releaseTime"/>
        <result column="title" property="title"/>
        <result column="content" property="content"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="baseColumnList">
        select id,
        create_user AS createUser,
        create_time AS createTime,
        update_user AS updateUser,
        update_time AS updateTime,
        status,
        is_deleted AS isDeleted,
        title, content
    </sql>

    <select id="topList" resultMap="noticeResultMap">
        select * from blade_notice limit #{number}
    </select>

    <select id="selectNoticePage" resultMap="noticeResultMap">
        select * from blade_notice where title like concat('%', #{notice.title}, '%') and is_deleted = 0
    </select>

</mapper>
