package org.springblade.miniapp.service.impl;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.business.report.entity.Report;
import org.springblade.business.report.service.IReportService;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.miniapp.service.WeChatReportService;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@AllArgsConstructor
public class WeChatReportServiceImpl implements WeChatReportService{
	private final IReportService reportService;
	@Override
	public Long saveReport(Report report) {
		Report report1 = new Report();
		report1.setPostId(report.getPostId());
		report1.setUserId(AuthUtil.getUserId());
		report1.setContent(report.getContent());
		report1.setAuditStatus("0");
		report1.setImages(report.getImages());
		reportService.save(report1);
		return report.getId();
	}

	@Override
	public Long saveOrUpdateReport(Report report) {
		if (!AuthUtil.isAdministrator()) {
			report.setUserId(AuthUtil.getUserId());
		}
		if(report.getId() == null) {
			return this.saveReport(report);
		}else{
			reportService.updateById(report);
			return report.getId();
		}
	}



}
