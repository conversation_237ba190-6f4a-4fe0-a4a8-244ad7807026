package org.springblade.miniapp.controller.open;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.business.institution.entity.InstitutionType;
import org.springblade.business.institution.vo.InstitutionVO;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.miniapp.service.WeChatIInstitutionService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 机构开放接口
 *
 * <AUTHOR>
 * @version 1.0
 * @email <EMAIL>
 * @website duofan.top
 * @date 2025/7/29
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/blade-chat-open/institution")
@io.swagger.v3.oas.annotations.tags.Tag(name = "小程序开放机构接口", description = "机构开放接口")
public class WeChatInstitutionOpenController {

	@Resource
	private WeChatIInstitutionService institutionService;

	/**
	 * 根据机构id查询机构信息并且关联帖子来展示这个机构所有的帖子
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入机构")
	public R<InstitutionVO> detail(@Valid InstitutionVO institution) {
		InstitutionVO vo = institutionService.getInstitutionDetailWithPosts(institution);
		return R.data(vo);
	}
	/**
	 * 根据机构id查询机构信息并且关联帖子来展示这个机构所有的帖子
	 */
	@GetMapping("/detail/{id}")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入机构")
	public R<InstitutionVO> detailById(@PathVariable Long id) {
		InstitutionVO institutionVO = new InstitutionVO();
		institutionVO.setId(id);
		InstitutionVO vo = institutionService.getInstitutionDetailWithPosts(institutionVO);
		return R.data(vo);
	}

	/**
	 * 自定义分页 机构主表   支持根据机构名和机构分类来查询和机构描述
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入institution")
	public R<IPage<InstitutionVO>> page(InstitutionVO institution, Query query) {
		IPage<InstitutionVO> pages = institutionService.getInstitutionPage(institution,query);
		return R.data(pages);
	}

	/**
	 * 获取机构分类
	 */
	@GetMapping("/type/list")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页")
	public R<List<InstitutionType>> typeList() {
		List<InstitutionType> pages = institutionService.getInstitutionTypes();
		return R.data(pages);
	}

}
