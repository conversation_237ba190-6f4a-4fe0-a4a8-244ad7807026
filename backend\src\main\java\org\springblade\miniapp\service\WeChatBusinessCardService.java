package org.springblade.miniapp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import jakarta.validation.Valid;
import org.springblade.business.user.entity.BusinessCard;
import org.springblade.business.user.vo.BusinessCardVO;
import org.springblade.core.mp.support.Query;
import org.springblade.miniapp.command.BusinessCardUpdateCommand;

import java.util.List;

public interface WeChatBusinessCardService {

	IPage<BusinessCardVO> getHomeCartList(Query query, BusinessCardVO businessCard);

	boolean create(BusinessCardUpdateCommand businessCard);

	List<BusinessCard> getCardByUserId(Long userId);

	boolean delete(List<Long> ids);

	boolean update(@Valid BusinessCardUpdateCommand businessCard);

	/**
	 * 切换名片收藏状态
	 * @param cardId 名片ID
	 * @return 操作后的状态：true-已收藏，false-已取消收藏
	 */
	boolean toggleCardFavorite(Long cardId);

	/**
	 * 检查名片是否已收藏
	 * @param cardId 名片ID
	 * @return 是否已收藏
	 */
	boolean isCardFavorited(Long cardId);
}
