/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<PERSON><PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.modules.system.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.system.entity.Param;
import org.springblade.modules.system.mapper.ParamMapper;
import org.springblade.modules.system.service.IParamService;
import org.springblade.modules.system.vo.ParamVO;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;

import static org.springblade.common.cache.CacheNames.WECHAT_PARAM_ALL;

/**
 * 服务实现类
 *
 * <AUTHOR>
 */
@Service
public class ParamServiceImpl extends BaseServiceImpl<ParamMapper, Param> implements IParamService {

	@Override
	public IPage<ParamVO> selectParamPage(IPage<ParamVO> page, ParamVO param) {
		return page.setRecords(baseMapper.selectParamPage(page, param));
	}


	@Override
	@Cacheable(cacheNames = WECHAT_PARAM_ALL)
	public String getByKey(String key) {
		Param param = new Param();
		param.setParamKey(key);
		Param one = baseMapper.selectOne(Condition.getQueryWrapper(param));
		return one.getParamValue();
	}

	@Override
	public Param getParamDetail(Param param) {
		return this.getOne(Condition.getQueryWrapper(param));
	}

	@Override
	@Caching(evict = {
		@CacheEvict(cacheNames = WECHAT_PARAM_ALL, allEntries = true)
	})
	public boolean saveOrUpdateParam(Param param) {
		return this.saveOrUpdate(param);
	}

	@Override
	@Caching(evict = {
		@CacheEvict(cacheNames = WECHAT_PARAM_ALL, allEntries = true)
	})
	public boolean deleteLogicParam(String ids) {
		return this.deleteLogic(Func.toLongList(ids));
	}

}
