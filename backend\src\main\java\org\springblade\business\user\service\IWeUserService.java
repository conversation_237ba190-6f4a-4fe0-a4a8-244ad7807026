/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.business.statistics.entity.UserRequestStats;
import org.springblade.business.user.entity.WeUser;
import org.springblade.business.user.vo.WeUserVO;
import org.springblade.core.mp.base.BaseService;
import org.springblade.modules.system.vo.UserVO;

import java.util.List;
import java.util.Map;

/**
 * 用户信息 服务类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
public interface IWeUserService extends BaseService<WeUser> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param user
	 * @return
	 */
	IPage<WeUserVO> selectUserPage(IPage<WeUserVO> page, WeUserVO user);

	void createNewUser(Long id, String tenantId);

    List<Map<String , Object>> getUserRequestStats(String startDate, String endDate);

	List<UserRequestStats> getUserRequestStatsTop20(String date);

	WeUserVO getDetail(WeUserVO user);

    Integer getPoints(Long userId);
}
