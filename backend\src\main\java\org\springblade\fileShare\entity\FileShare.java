package org.springblade.fileShare.entity;

import java.io.Serial;
import java.time.LocalDateTime;

import org.springblade.core.mp.base.BaseEntity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文件分享实体类
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@TableName("file_share")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "文件分享对象")
public class FileShare extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 分享密钥（6位字符）
     */
    @Schema(description = "分享密钥")
    private String shareKey;

    /**
     * 分享类型（FILE-文件，TEXT-文本）
     */
    @Schema(description = "分享类型")
    private String shareType;

    /**
     * 文件名称
     */
    @Schema(description = "文件名称")
    private String fileName;

    /**
     * 文件路径
     */
    @Schema(description = "文件路径")
    private String filePath;

    /**
     * 文件大小（字节）
     */
    @Schema(description = "文件大小")
    private Long fileSize;

    /**
     * 文件类型
     */
    @Schema(description = "文件类型")
    private String fileType;

    /**
     * 文本内容
     */
    @Schema(description = "文本内容")
    private String textContent;

    /**
     * 过期时间
     */
    @Schema(description = "过期时间")
    private LocalDateTime expireTime;

    /**
     * 下载次数
     */
    @Schema(description = "下载次数")
    private Integer downloadCount;

    /**
     * 状态（0-正常，1-已过期，2-已删除）
     */
    @Schema(description = "状态")
    private Integer status;
}
