package org.springblade.miniapp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.business.post.entity.Category;
import org.springblade.business.post.vo.CategoryVO;
import org.springblade.core.mp.support.Query;

import java.util.List;

public interface WeChatCategoryService {

	IPage<CategoryVO> getCategoryList(Query query);

	/**
	 * 获取启用的分类列表
	 *
	 * @return 分类列表
	 */
	List<Category> getEnabledCategories();

	/**
	 * 清理分类相关缓存
	 */
	void clearCategoryCache();
}
