/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.institution.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import org.springblade.business.institution.dto.InstitutionStatsDTO;
import org.springblade.business.institution.entity.Institution;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springblade.business.institution.entity.InstitutionAuditLog;
import org.springblade.business.post.vo.SupPostVO;

import java.io.Serial;
import java.util.List;

/**
 * 机构主表视图实体类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "机构主表")
public class InstitutionVO extends Institution {
	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 帖子列表
	 */
	@Schema(description = "帖子列表")
	private List<SupPostVO> posts;

	/**
	 * 机构名
	 */
	@TableField(exist = false)
	@Schema(description = "机构分类名")
	private String typeName;

	/**
	 * 帖子总数
	 */
	@TableField(exist = false)
	@Schema(description = "帖子总数")
	private Integer postCount;

	/**
	 * 审核信息
	 */
	@TableField(exist = false)
	@Schema(description = "审核信息")
	private InstitutionAuditLog auditLog;


	/**
	 * 机构统计信息
	 */
	@TableField(exist = false)
	@Schema(description = "机构统计信息")
	private InstitutionStatsDTO institutionStats;


}
