package org.springblade.miniapp.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据操作状态 DTO
 * 用于返回点赞、收藏等状态查询结果
 *
 * <AUTHOR>
 * @version 1.0
 * @email <EMAIL>
 * @website duofan.top
 * @date 2025/7/31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "数据操作状态")
public class DataOperateStatusDTO {

    @Schema(description = "是否已点赞")
    private Boolean isLiked;

    @Schema(description = "是否已收藏")
    private Boolean isFavorited;

    /**
     * 创建点赞状态结果
     */
    public static DataOperateStatusDTO createLikeStatus(boolean isLiked) {
        return DataOperateStatusDTO.builder()
                .isLiked(isLiked)
                .build();
    }

    /**
     * 创建收藏状态结果
     */
    public static DataOperateStatusDTO createFavoriteStatus(boolean isFavorited) {
        return DataOperateStatusDTO.builder()
                .isFavorited(isFavorited)
                .build();
    }
}
