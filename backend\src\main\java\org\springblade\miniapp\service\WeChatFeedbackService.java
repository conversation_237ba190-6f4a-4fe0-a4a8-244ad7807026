package org.springblade.miniapp.service;

import org.springblade.business.painpoint.entity.PainPoint;
import org.springblade.business.post.dto.CommentDTO;
import org.springblade.business.post.entity.Feedback;
import org.springblade.business.post.vo.FeedbackVO;
import org.springblade.core.mp.support.Query;

import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

public interface WeChatFeedbackService {

	/**
	 * 提交反馈
	 */
	Long submit(Feedback feedback);

	/**
	 * 分页查询
	 */
	IPage<FeedbackVO> page(IPage<FeedbackVO> page, FeedbackVO feedback);

	/**
	 * 删除反馈
	 */
	boolean remove(String ids);

	/**
	 * 标记反馈是否有帮助
	 *
	 * @param id 反馈ID
	 * @return 操作结果
	 */
	boolean toggleHelpful(Long id);

	/**
	 * 新增程序反馈
	 */
	boolean save(PainPoint painPoint);

	/**
	 * 添加评论
	 *
	 * @param commentDTO 评论数据
	 * @return 评论ID
	 */
	Long addComment(CommentDTO commentDTO);

	/**
	 * 回复评论
	 *
	 * @param commentDTO 回复数据
	 * @return 回复ID
	 */
	Long replyComment(CommentDTO commentDTO);

	/**
	 * 获取评论列表（包含回复）
	 *
	 * @param postId 帖子ID
	 * @param query 查询参数
	 * @return 评论列表
	 */
	IPage<FeedbackVO> getCommentList(Long postId, Query query);

	/**
	 * 获取评论回复列表
	 *
	 * @param parentId 父评论ID
	 * @return 回复列表
	 */
	List<FeedbackVO> getCommentReplies(Long parentId);

	/**
	 * 删除评论
	 *
	 * @param commentId 评论ID
	 * @return 操作结果
	 */
	boolean removeComment(Long commentId);

	/**
	 * 点赞/取消点赞评论
	 *
	 * @param commentId 评论ID
	 * @param likeType 点赞类型
	 * @return 操作结果
	 */
	boolean toggleCommentLike(Long commentId, String likeType);
}
