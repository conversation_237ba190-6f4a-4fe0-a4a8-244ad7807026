<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.user.mapper.UserLoginStatsMapper">

    <insert id="upsert">
        INSERT INTO user_login_stats
            (user_id, last_login_time, today_login_count, sync_date, create_time)
        VALUES (#{userId}, #{lastLoginTime}, #{todayLoginCount}, #{syncDate}, NOW())
            ON DUPLICATE KEY UPDATE
                                 last_login_time = VALUES(last_login_time),
                                 today_login_count = VALUES(today_login_count),
                                 update_time = NOW()
    </insert>

    <insert id="batchUpsert">
        INSERT INTO user_login_stats
        (user_id, last_login_time, today_login_count , sync_time,create_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.userId}, #{item.lastLoginTime}, #{item.todayLoginCount},#{item.syncTime}, NOW())
        </foreach>
        ON DUPLICATE KEY UPDATE
        last_login_time = VALUES(last_login_time),
        today_login_count = VALUES(today_login_count),
        update_time = NOW()
    </insert>
</mapper>
