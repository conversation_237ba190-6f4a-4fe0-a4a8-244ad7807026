package org.springblade.common.convert;

import cn.hutool.core.collection.CollUtil;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeException;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.URL;
import java.sql.*;
import java.time.*;
import java.util.Arrays;
import java.util.Calendar;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.springframework.stereotype.Component;

import java.sql.*;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class ListToStringTypeHandler extends BaseTypeHandler<List<String>> {

	// 将 Java 对象转换为数据库存储的字符串
	@Override
	public void setNonNullParameter(PreparedStatement ps, int i, List<String> parameter, JdbcType jdbcType) throws SQLException {
		String result = String.join(",", parameter);
		ps.setString(i, result);
	}

	// 从数据库结果集中获取字符串并转换为 List<String>
	@Override
	public List<String> getNullableResult(ResultSet rs, String columnName) throws SQLException {
		String result = rs.getString(columnName);
		return result != null ? Arrays.asList(result.split(",")) : null;
	}

	@Override
	public List<String> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
		String result = rs.getString(columnIndex);
		return result != null ? Arrays.asList(result.split(",")) : null;
	}

	@Override
	public List<String> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
		String result = cs.getString(columnIndex);
		return result != null ? Arrays.asList(result.split(",")) : null;
	}
}
