package org.springblade.common.config;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;

@Configuration
public class JsonConfiguration {

	/**
	 * 默认日期时间格式
	 */
	public static final String DEFAULT_DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
	/**
	 * 默认日期格式
	 */
	public static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd";
	/**
	 * 默认时间格式
	 */
	public static final String DEFAULT_TIME_FORMAT = "HH:mm:ss";

	@Bean
	public ObjectMapper objectMapper() {
		return Jackson2ObjectMapperBuilder.json()
				.modules(
						getJavaTimeModule(),
						getSimpleModule())
				.build();
	}

	/**
	 * 处理日期时间序列化
	 */
	private JavaTimeModule getJavaTimeModule() {
		JavaTimeModule javaTimeModule = new JavaTimeModule();

		// LocalDateTime序列化和反序列化
		DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DEFAULT_DATE_TIME_FORMAT)
				.withZone(java.time.ZoneId.of("Asia/Shanghai"));
		javaTimeModule.addSerializer(LocalDateTime.class,
				new LocalDateTimeSerializer(dateTimeFormatter));
		javaTimeModule.addDeserializer(LocalDateTime.class,
				new LocalDateTimeDeserializer(dateTimeFormatter));

		// LocalDate序列化和反序列化
		DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(DEFAULT_DATE_FORMAT)
				.withZone(java.time.ZoneId.of("Asia/Shanghai"));
		javaTimeModule.addSerializer(LocalDate.class,
				new LocalDateSerializer(dateFormatter));
		javaTimeModule.addDeserializer(LocalDate.class,
				new LocalDateDeserializer(dateFormatter));

		// LocalTime序列化和反序列化
		DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern(DEFAULT_TIME_FORMAT)
				.withZone(java.time.ZoneId.of("Asia/Shanghai"));
		javaTimeModule.addSerializer(LocalTime.class,
				new LocalTimeSerializer(timeFormatter));
		javaTimeModule.addDeserializer(LocalTime.class,
				new LocalTimeDeserializer(timeFormatter));

		return javaTimeModule;
	}

	/**
	 * 处理Long类型转String
	 */
	private SimpleModule getSimpleModule() {
		SimpleModule simpleModule = new SimpleModule();
		// Long类型转String
		simpleModule.addSerializer(Long.class, ToStringSerializer.instance);
		simpleModule.addSerializer(Long.TYPE, ToStringSerializer.instance);
		return simpleModule;
	}
}
