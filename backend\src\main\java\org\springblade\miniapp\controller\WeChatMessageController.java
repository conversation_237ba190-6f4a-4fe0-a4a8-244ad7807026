package org.springblade.miniapp.controller;

import org.springblade.business.post.dto.MessageQueryDTO;
import org.springblade.business.post.dto.MessageTemplateCreateDTO;
import org.springblade.business.post.dto.MessageTemplateUpdateDTO;
import org.springblade.common.anno.AccessLimit;
import org.springblade.miniapp.service.WeChatMessageService;
import org.springblade.miniapp.vo.*;
import org.springblade.core.tool.api.R;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

@RestController
@RequestMapping("/blade-chat/message")
@RequiredArgsConstructor
@Tag(name = "小程序-消息模块", description = "消息管理相关接口")
public class WeChatMessageController {

    private final WeChatMessageService messageService;

	/**
     * 查询消息列表
     */
	@AccessLimit(seconds = 30)
    @GetMapping
    @Operation(summary = "分页查询消息列表", description = "支持标题、内容、类型、已读状态、时间范围等多条件分页查询")
    public R<IPage<MessageVO>> list(@Parameter(description = "查询参数DTO") MessageQueryDTO queryDTO) {
        return R.data(messageService.queryMessages(queryDTO));
    }

    /**
     * 标记消息为已读
     */
    @PutMapping("/{id}/read")
    @Operation(summary = "标记消息为已读", description = "根据消息ID将消息状态设为已读")
    public R<?> markAsRead(@Parameter(description = "消息ID", required = true) @PathVariable Long id) {
        messageService.markAsRead(id);
        return R.success("操作成功");
    }

    /**
     * 批量删除消息
     */
    @DeleteMapping
    @Operation(summary = "批量删除消息", description = "根据ID列表批量删除消息")
    public R<?> batchDelete(@Parameter(description = "消息ID列表", required = true) @RequestBody List<Long> ids) {
        messageService.batchDelete(ids);
        return R.success("删除成功");
    }

    /**
     * 创建消息
     */
//    @PostMapping
//    @Operation(summary = "创建消息", description = "根据模板和参数创建新消息")
//    public R<MessageVO> create(@Parameter(description = "消息创建DTO", required = true) @RequestBody MessageCreateDTO createDTO) {
//        return R.data(messageService.createMessage(createDTO));
//    }


    /**
     * 获取所有消息模板,管理员可用
     */
    @GetMapping("/templates")
	@AccessLimit(seconds = 30)
    @Operation(summary = "获取所有消息模板", description = "返回所有可用的消息模板列表")
    public R<List<MessageTemplateVO>> getTemplates() {
        return R.data(messageService.getAllTemplates());
    }

    /**
     * 更新消息模板,管理员可用
     */
    @PutMapping("/templates")
	@AccessLimit(seconds = 30)
    @Operation(summary = "更新消息模板", description = "根据ID更新消息模板内容和状态")
    public R<?> updateTemplate(
            @Parameter(description = "模板更新DTO", required = true) @RequestBody MessageTemplateUpdateDTO updateDTO) {
        messageService.updateTemplate(updateDTO);
        return R.success("更新成功");
    }

    /**
     * 新增消息模板，管理员
     */
    @PostMapping("/templates")
	@AccessLimit(seconds = 30)
    @Operation(summary = "新增消息模板", description = "创建一个新的消息模板")
    public R<?> addTemplate(@Parameter(description = "新增消息模板DTO", required = true) @RequestBody MessageTemplateCreateDTO createDTO) {
        messageService.addTemplate(createDTO);
        return R.success("新增成功");
    }
}
