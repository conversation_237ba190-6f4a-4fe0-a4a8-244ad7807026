
package org.springblade.miniapp.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tool.node.INode;

import java.io.Serializable;
import java.util.List;

/**
 * 消息模板视图对象
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "消息模板视图对象")
public class MessageTemplateVO implements  Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 类型编码
     */
    @Schema(description = "消息类型编码")
    private String typeCode;

    /**
     * 类型名称
     */
    @Schema(description = "消息类型名称")
    private String typeName;

    /**
     * 标题模板
     */
    @Schema(description = "消息标题模板")
    private String titleTemplate;

    /**
     * 内容模板
     */
    @Schema(description = "消息内容模板")
    private String contentTemplate;

    /**
     * 是否系统模板
     */
    @Schema(description = "是否为系统内置模板")
    private Boolean isSystem;

    /**
     * 状态(1:启用 0:禁用)
     */
    @Schema(description = "模板状态(1:启用 0:禁用)")
    private Integer status;


}
