/**
 * Copyright (c) 2018-2099, Chill <PERSON>ang 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.modules.system.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;

/**
 * 云存储服务接口
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
public interface ICloudStorageService {

    /**
     * 上传文件
     *
     * @param file 文件
     * @param path 存储路径
     * @return 访问URL
     */
    String uploadFile(MultipartFile file, String path);

    /**
     * 上传文件流
     *
     * @param inputStream 文件流
     * @param fileName    文件名
     * @param path        存储路径
     * @param contentType 文件类型
     * @return 访问URL
     */
    String uploadFile(InputStream inputStream, String fileName, String path, String contentType);

    /**
     * 上传字节数组
     *
     * @param data        字节数组
     * @param fileName    文件名
     * @param path        存储路径
     * @param contentType 文件类型
     * @return 访问URL
     */
    String uploadFile(byte[] data, String fileName, String path, String contentType);

    /**
     * 删除文件
     *
     * @param path 文件路径
     * @return 是否成功
     */
    boolean deleteFile(String path);

    /**
     * 获取文件访问URL
     *
     * @param path 文件路径
     * @return 访问URL
     */
    String getFileUrl(String path);

    /**
     * 生成缩略图
     *
     * @param originalPath 原文件路径
     * @param thumbnailPath 缩略图路径
     * @param width 宽度
     * @param height 高度
     * @return 缩略图URL
     */
    String generateThumbnail(String originalPath, String thumbnailPath, int width, int height);
} 