package org.springblade.miniapp.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springblade.business.user.entity.BusinessCard;
import org.springblade.business.user.service.IBusinessCardFavoriteService;
import org.springblade.business.user.vo.BusinessCardFavoriteVO;
import org.springblade.business.user.vo.BusinessCardVO;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.miniapp.command.BusinessCardUpdateCommand;
import org.springblade.miniapp.service.WeChatBusinessCardService;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/blade-chat/card")
@io.swagger.v3.oas.annotations.tags.Tag(name = "小程序个人名片接口")
public class WeChatBusinessCardController {
	@Resource
	private WeChatBusinessCardService businessCardService;

	@Resource
	private IBusinessCardFavoriteService businessCardFavoriteService;

	/**
	 * 修改 名片信息表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description = "传入businessCard")
	public R update(@Valid @RequestBody BusinessCardUpdateCommand businessCard) {
		return R.status(businessCardService.update(businessCard));
	}

	/**
	 * 提交名片
	 */
	@PostMapping("/create")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description = "传入businessCard")
	public R submit(@Valid @RequestBody BusinessCardUpdateCommand businessCard) {
		return R.status(businessCardService.create(businessCard));
	}

	/**
	 * 获取当前用户名片
	 */
	@GetMapping("my-card")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "获取当前用户名片", description = "获取当前登录用户的名片信息")
	public R<List<BusinessCard>> getMyCard() {
		List<BusinessCard> myCard = businessCardService.getCardByUserId(AuthUtil.getUserId());
		return R.data(myCard);
	}

	/**
	 * 删除 名片信息表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(businessCardService.delete(Func.toLongList(ids)));
	}

	/**
	 * 分页查询我的收藏名片列表
	 */
	@GetMapping("/favorite/list")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "分页查询我的收藏名片列表", description = "查询当前用户收藏的名片")
	public R<IPage<BusinessCardFavoriteVO>> getMyFavoriteCardList(BusinessCardVO businessCard, Query query) {
		try {
			IPage<BusinessCardFavoriteVO> pages = businessCardFavoriteService.getMyFavoriteCardList(query, businessCard);
			return R.data(pages);
		} catch (Exception e) {
			return R.fail("查询失败：" + e.getMessage());
		}
	}

	/**
	 * 收藏名片（使用通用数据操作接口）
	 */
	@PostMapping("/favorite/{cardId}")
	@ApiOperationSupport(order = 10)
	@Operation(summary = "收藏名片", description = "收藏指定名片到我的名片库")
	public R<Boolean> favoriteCard(
			@PathVariable("cardId") Long cardId,
			@RequestParam(value = "category", required = false) String category,
			@RequestParam(value = "remark", required = false) String remark) {
		try {
			Boolean result = businessCardFavoriteService.favoriteCard(cardId, category, remark);
			return R.data(result);
		} catch (Exception e) {
			return R.fail("收藏失败：" + e.getMessage());
		}
	}

	/**
	 * 取消收藏名片（使用通用数据操作接口）
	 */
	@DeleteMapping("/favorite/{cardId}")
	@ApiOperationSupport(order = 11)
	@Operation(summary = "取消收藏名片", description = "从我的名片库中移除指定名片")
	public R<Boolean> unfavoriteCard(@PathVariable("cardId") Long cardId) {
		try {
			Boolean result = businessCardFavoriteService.unfavoriteCard(cardId);
			return R.data(result);
		} catch (Exception e) {
			return R.fail("取消收藏失败：" + e.getMessage());
		}
	}
	/**
	 * 检查名片收藏和点赞状态（使用通用数据操作接口）
	 */
	@GetMapping("/status/{cardId}")
	@ApiOperationSupport(order = 13)
	@Operation(summary = "检查名片状态", description = "检查名片的收藏和点赞状态")
	public R<Object> getCardStatus(@PathVariable("cardId") Long cardId) {
		try {
			Boolean isFavorited = businessCardFavoriteService.isCardFavorited(cardId);
			Boolean isLiked = businessCardFavoriteService.isCardLiked(cardId);
			R<Object> data = R.data(Map.of	("isFavorited",isFavorited,"isLiked",isLiked));
			return data;
		} catch (Exception e) {
			return R.fail("查询状态失败：" + e.getMessage());
		}
	}
}
