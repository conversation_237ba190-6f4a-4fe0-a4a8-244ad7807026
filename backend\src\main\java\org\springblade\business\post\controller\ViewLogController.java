//package org.springblade.business.post.controller;
//
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
//import io.swagger.v3.oas.annotations.Operation;
//import lombok.AllArgsConstructor;
//import org.springblade.core.mp.support.Condition;
//import org.springblade.core.mp.support.Query;
//import org.springblade.core.tool.api.R;
//import org.springblade.core.tool.utils.Func;
//import org.springblade.business.post.entity.ViewLog;
//import org.springblade.business.post.service.IViewLogService;
//import org.springblade.business.post.vo.ViewLogVO;
//import org.springblade.business.post.wrapper.ViewLogWrapper;
//import org.springframework.web.bind.annotation.*;
//
//import javax.validation.Valid;
//
///**
// * 浏览记录 控制器
// *
// * <AUTHOR>
// * @since 2025-08-01
// */
//@RestController
//@AllArgsConstructor
//@RequestMapping("/blade-ad/viewlog")
//@io.swagger.v3.oas.annotations.tags.Tag(name = "浏览记录", description = "浏览记录接口")
//public class ViewLogController {
//
//	private IViewLogService viewLogService;
//
//	/**
//	 * 详情
//	 */
//	@GetMapping("/detail")
//	@ApiOperationSupport(order = 1)
//	@Operation(summary = "详情", description = "传入viewLog")
//	public R<ViewLogVO> detail(ViewLog viewLog) {
//		ViewLog detail = viewLogService.getOne(Condition.getQueryWrapper(viewLog));
//		return R.data(ViewLogWrapper.build().entityVO(detail));
//	}
//
//	/**
//	 * 分页 浏览记录
//	 */
//	@GetMapping("/list")
//	@ApiOperationSupport(order = 2)
//	@Operation(summary = "分页", description = "传入viewLog")
//	public R<IPage<ViewLogVO>> list(ViewLog viewLog, Query query) {
//		IPage<ViewLog> pages = viewLogService.page(Condition.getPage(query), Condition.getQueryWrapper(viewLog));
//		return R.data(ViewLogWrapper.build().pageVO(pages));
//	}
//
//	/**
//	 * 自定义分页 浏览记录
//	 */
//	@GetMapping("/page")
//	@ApiOperationSupport(order = 3)
//	@Operation(summary = "分页", description = "传入viewLog")
//	public R<IPage<ViewLogVO>> page(ViewLogVO viewLog, Query query) {
//		IPage<ViewLogVO> pages = viewLogService.selectViewLogPage(Condition.getPage(query), viewLog);
//		return R.data(pages);
//	}
//
//	/**
//	 * 新增 浏览记录
//	 */
//	@PostMapping("/save")
//	@ApiOperationSupport(order = 4)
//	@Operation(summary = "新增", description = "传入viewLog")
//	public R save(@Valid @RequestBody ViewLog viewLog) {
//		return R.status(viewLogService.save(viewLog));
//	}
//
//	/**
//	 * 修改 浏览记录
//	 */
//	@PostMapping("/update")
//	@ApiOperationSupport(order = 5)
//	@Operation(summary = "修改", description = "传入viewLog")
//	public R update(@Valid @RequestBody ViewLog viewLog) {
//		return R.status(viewLogService.updateById(viewLog));
//	}
//
//	/**
//	 * 新增或修改 浏览记录
//	 */
//	@PostMapping("/submit")
//	@ApiOperationSupport(order = 6)
//	@Operation(summary = "新增或修改", description = "传入viewLog")
//	public R submit(@Valid @RequestBody ViewLog viewLog) {
//		return R.status(viewLogService.saveOrUpdate(viewLog));
//	}
//
//	/**
//	 * 删除 浏览记录
//	 */
//	@PostMapping("/remove")
//	@ApiOperationSupport(order = 7)
//	@Operation(summary = "逻辑删除", description = "传入ids")
//	public R remove(@RequestParam String ids) {
//		return R.status(viewLogService.deleteLogic(Func.toLongList(ids)));
//	}
//
//}
