package org.springblade.core.tool.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 性能优化工具类
 * 提供常用的性能优化方法和监控工具
 *
 * <AUTHOR>
 * @version 1.0
 * @email <EMAIL>
 * @website duofan.top
 * @date 2025/8/1
 */
@Slf4j
public class PerformanceUtils {

    /**
     * 批量查询优化 - 避免N+1查询问题
     * 
     * @param items 需要处理的数据列表
     * @param keyExtractor 提取关联键的函数
     * @param batchQuery 批量查询函数
     * @param dataSetter 数据设置函数
     * @param <T> 主数据类型
     * @param <K> 关联键类型
     * @param <V> 关联数据类型
     */
    public static <T, K, V> void batchQueryAndSet(
            List<T> items,
            Function<T, K> keyExtractor,
            Function<Set<K>, Map<K, V>> batchQuery,
            BiConsumer<T, V> dataSetter) {
        
        if (items == null || items.isEmpty()) {
            return;
        }

        // 收集所有需要查询的键
        Set<K> keys = items.stream()
                .map(keyExtractor)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (keys.isEmpty()) {
            return;
        }

        // 批量查询
        Map<K, V> dataMap = batchQuery.apply(keys);

        // 设置数据
        items.forEach(item -> {
            K key = keyExtractor.apply(item);
            if (key != null) {
                V value = dataMap.get(key);
                dataSetter.accept(item, value);
            }
        });
    }

    /**
     * 方法执行时间监控
     * 
     * @param methodName 方法名
     * @param task 要执行的任务
     * @param <T> 返回值类型
     * @return 任务执行结果
     */
    public static <T> T monitorExecution(String methodName, Supplier<T> task) {
        StopWatch stopWatch = new StopWatch(methodName);
        stopWatch.start();
        
        try {
            T result = task.get();
            stopWatch.stop();
            
            long totalTimeMillis = stopWatch.getTotalTimeMillis();
            if (totalTimeMillis > 1000) {
                log.warn("方法 {} 执行耗时: {}ms", methodName, totalTimeMillis);
            } else if (log.isDebugEnabled()) {
                log.debug("方法 {} 执行耗时: {}ms", methodName, totalTimeMillis);
            }
            
            return result;
        } catch (Exception e) {
            stopWatch.stop();
            log.error("方法 {} 执行失败，耗时: {}ms", methodName, stopWatch.getTotalTimeMillis(), e);
            throw e;
        }
    }

    /**
     * 无返回值方法执行时间监控
     * 
     * @param methodName 方法名
     * @param task 要执行的任务
     */
    public static void monitorExecution(String methodName, Runnable task) {
        monitorExecution(methodName, () -> {
            task.run();
            return null;
        });
    }

    /**
     * 分批处理大数据集，避免内存溢出
     * 
     * @param items 数据列表
     * @param batchSize 批次大小
     * @param processor 批次处理器
     * @param <T> 数据类型
     */
    public static <T> void processBatch(List<T> items, int batchSize, Consumer<List<T>> processor) {
        if (items == null || items.isEmpty()) {
            return;
        }

        for (int i = 0; i < items.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, items.size());
            List<T> batch = items.subList(i, endIndex);
            processor.accept(batch);
        }
    }

    /**
     * 集合去重并保持顺序
     * 
     * @param list 原始列表
     * @param keyExtractor 提取唯一键的函数
     * @param <T> 元素类型
     * @param <K> 键类型
     * @return 去重后的列表
     */
    public static <T, K> List<T> distinctByKey(List<T> list, Function<T, K> keyExtractor) {
        Set<K> seen = new HashSet<>();
        return list.stream()
                .filter(item -> seen.add(keyExtractor.apply(item)))
                .collect(Collectors.toList());
    }

    /**
     * 安全的集合操作 - 避免空指针异常
     * 
     * @param collection 集合
     * @param <T> 元素类型
     * @return 非空集合
     */
    public static <T> Collection<T> safeCollection(Collection<T> collection) {
        return collection != null ? collection : Collections.emptyList();
    }

    /**
     * 安全的Map操作 - 避免空指针异常
     * 
     * @param map Map对象
     * @param <K> 键类型
     * @param <V> 值类型
     * @return 非空Map
     */
    public static <K, V> Map<K, V> safeMap(Map<K, V> map) {
        return map != null ? map : Collections.emptyMap();
    }

    /**
     * 内存使用情况监控
     */
    public static void logMemoryUsage(String context) {
        if (log.isDebugEnabled()) {
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            long maxMemory = runtime.maxMemory();

            log.debug("内存使用情况 [{}] - 已用: {}MB, 空闲: {}MB, 总计: {}MB, 最大: {}MB",
                    context,
                    usedMemory / 1024 / 1024,
                    freeMemory / 1024 / 1024,
                    totalMemory / 1024 / 1024,
                    maxMemory / 1024 / 1024);
        }
    }

    /**
     * 数据库查询次数统计
     */
    public static class QueryCounter {
        private static final ThreadLocal<Integer> queryCount = new ThreadLocal<>();

        public static void reset() {
            queryCount.set(0);
        }

        public static void increment() {
            Integer count = queryCount.get();
            queryCount.set(count == null ? 1 : count + 1);
        }

        public static int getCount() {
            Integer count = queryCount.get();
            return count == null ? 0 : count;
        }

        public static void logAndReset(String operation) {
            int count = getCount();
            if (count > 0) {
                log.info("操作 [{}] 执行了 {} 次数据库查询", operation, count);
            }
            reset();
        }
    }

    // 函数式接口定义
    @FunctionalInterface
    public interface BiConsumer<T, U> {
        void accept(T t, U u);
    }

    @FunctionalInterface
    public interface Supplier<T> {
        T get();
    }

    @FunctionalInterface
    public interface Consumer<T> {
        void accept(T t);
    }
}
