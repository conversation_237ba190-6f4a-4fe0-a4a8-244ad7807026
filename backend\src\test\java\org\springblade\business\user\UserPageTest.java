package org.springblade.business.user;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * 用户分页查询测试
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
@SpringBootTest
@ActiveProfiles("test")
public class UserPageTest {

    @Test
    public void testUserPageQuery() {
        // 这里可以添加具体的测试逻辑
        // 测试用户分页查询是否包含发帖数量、最后活跃时间、签到天数、积分数量等字段
        System.out.println("用户分页查询优化测试");
    }
}
