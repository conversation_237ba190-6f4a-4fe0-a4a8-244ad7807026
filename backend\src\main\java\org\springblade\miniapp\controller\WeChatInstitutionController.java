/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.miniapp.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.miniapp.service.WeChatIInstitutionService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.business.institution.entity.Institution;
import org.springblade.business.institution.entity.InstitutionType;
import org.springblade.business.institution.vo.InstitutionVO;
import org.springblade.core.boot.ctrl.BladeController;
import java.util.List;

/**
 * 机构主表 控制器
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-chat/institution")
@io.swagger.v3.oas.annotations.tags.Tag(name = "小程序机构接口", description = "机构主表接口")
public class WeChatInstitutionController extends BladeController {

	@Resource
	private WeChatIInstitutionService institutionService;

	/**
	 *  申请 机构主表
	 */
	@PostMapping("/apply")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入institution")
	public R apply(@Valid @RequestBody Institution institution) {
		boolean result = institutionService.applyInstitution(institution);
		return R.status(result);
	}

	/**
	 * 修改 机构主表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description = "传入institution")
	public R update(@Valid @RequestBody Institution institution) {
		boolean result = institutionService.updateInstitutionById(institution);
		return R.status(result);
	}



	/**
	 * 启动和禁用 机构主表
	 */
	@PostMapping("/openOrClose")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "启动会或者禁用", description = "传入ids")
	public R openOrClose(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		boolean result = institutionService.openOrClose(ids);
		return R.status(result);
	}

	/**
	 * 查询自己加入的所有机构
	 */
	@GetMapping("/my-institution")
	@ApiOperationSupport(order = 8)
	@Operation(summary = "查询自己加入的所有机构", description = "传入用户id")
	public R<IPage<InstitutionVO>> myInstitution(Query query) {
		IPage<InstitutionVO> pages = institutionService.pageByUserId(Condition.getPage(query));
		return R.data(pages);
	}

	/**
	 * 获取机构分类列表
	 */
	@GetMapping("/types")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "获取机构分类列表", description = "获取所有可用的机构分类")
	public R<List<InstitutionType>> getInstitutionTypes() {
		try {
			List<InstitutionType> types = institutionService.getInstitutionTypes();
			return R.data(types);
		} catch (Exception e) {
			return R.fail("获取机构分类失败：" + e.getMessage());
		}
	}

}
