/**
 * Copyright (c) 2018-2099, Chill <PERSON><PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;

/**
 * 群信息表实体类
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
@TableName("urb_rent_house")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "群信息表")
public class RentHouse extends BaseEntity {

    @Serial
	private static final long serialVersionUID = 1L;

    /**
     * 关联帖子Id
     */
    @Schema(description = "关联帖子Id")
    private Long postId;
    /**
     * 房子名称
     */
    @Schema(description = "房子名称")
    private String houseName;
    /**
     * 租房类型（合租，整租）
     */
    @Schema(description = "租房类型（合租，整租）")
    private String rentType;
    /**
     * 户型（3室，4室）
     */
    @Schema(description = "户型（3室，4室）")
    private String houseType;
    /**
     * 卧室类型（主卧，次卧）
     */
    @Schema(description = "卧室类型（主卧，次卧）")
    private String bedroomType;
    /**
     * 房子大小
     */
    @Schema(description = "房子大小")
    private String houseSize;


	/**
	 * 租房价格
	 */
	@Schema(description = "租房价格")
	private String rentPrice;

	/**
	 * （1-出房，2-找房）
	 */
	@Schema(description = "（1-出房，2-找房）")
	private String type;

}
