-- 为反馈系统添加评论回复支持
-- 扩展现有的 urb_feedback 表以支持多级评论回复

-- 1. 为 urb_feedback 表添加回复相关字段
ALTER TABLE `urb_feedback` 
ADD COLUMN `parent_id` bigint DEFAULT NULL COMMENT '父评论ID，NULL表示顶级评论' AFTER `post_id`,
ADD COLUMN `reply_to_user_id` bigint DEFAULT NULL COMMENT '被回复的用户ID' AFTER `parent_id`,
ADD COLUMN `reply_to_user_name` varchar(100) DEFAULT NULL COMMENT '被回复的用户昵称（冗余字段，便于显示）' AFTER `reply_to_user_id`,
ADD COLUMN `comment_type` varchar(20) DEFAULT 'FEEDBACK' COMMENT '评论类型：FEEDBACK-反馈，COMMENT-评论，REPLY-回复' AFTER `reply_to_user_name`,
ADD COLUMN `level` int DEFAULT 1 COMMENT '评论层级，1为顶级评论' AFTER `comment_type`,
ADD COLUMN `reply_count` int DEFAULT 0 COMMENT '回复数量' AFTER `level`,
ADD COLUMN `image` varchar(500) DEFAULT NULL COMMENT '评论图片URL' AFTER `content`,
ADD COLUMN `contact_info` varchar(255) DEFAULT NULL COMMENT '联系方式' AFTER `image`;

-- 2. 添加索引以提高查询性能
ALTER TABLE `urb_feedback`
ADD INDEX `idx_parent_id` (`parent_id`),
ADD INDEX `idx_reply_to_user_id` (`reply_to_user_id`),
ADD INDEX `idx_comment_type` (`comment_type`),
ADD INDEX `idx_level` (`level`),
ADD INDEX `idx_post_parent` (`post_id`, `parent_id`);

-- 3. 创建评论提及表（@用户功能）
CREATE TABLE `urb_comment_mention` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `comment_id` bigint NOT NULL COMMENT '评论ID',
  `mentioned_user_id` bigint NOT NULL COMMENT '被提及的用户ID',
  `mentioned_user_name` varchar(100) NOT NULL COMMENT '被提及的用户昵称',
  `create_time` datetime(6) DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_comment_user` (`comment_id`, `mentioned_user_id`),
  KEY `idx_comment_id` (`comment_id`),
  KEY `idx_mentioned_user_id` (`mentioned_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论提及表';

-- 4. 创建评论点赞表（扩展现有的helpful功能）
-- 重命名现有表以保持一致性
RENAME TABLE `urb_feedback_helpful` TO `urb_comment_like`;

-- 为点赞表添加更多字段
ALTER TABLE `urb_comment_like`
ADD COLUMN `like_type` varchar(20) DEFAULT 'HELPFUL' COMMENT '点赞类型：HELPFUL-有帮助，LIKE-点赞' AFTER `feedback_id`,
ADD COLUMN `update_time` datetime(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间' AFTER `create_time`;

-- 重命名字段以保持一致性
ALTER TABLE `urb_comment_like` 
CHANGE COLUMN `feedback_id` `comment_id` bigint NOT NULL COMMENT '评论ID';

-- 5. 创建评论举报表
CREATE TABLE `urb_comment_report` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `comment_id` bigint NOT NULL COMMENT '评论ID',
  `reporter_user_id` bigint NOT NULL COMMENT '举报用户ID',
  `report_reason` varchar(100) NOT NULL COMMENT '举报原因',
  `report_content` text COMMENT '举报详细内容',
  `report_status` varchar(20) DEFAULT 'PENDING' COMMENT '举报状态：PENDING-待处理，APPROVED-已处理，REJECTED-已驳回',
  `process_user_id` bigint DEFAULT NULL COMMENT '处理人ID',
  `process_time` datetime(6) DEFAULT NULL COMMENT '处理时间',
  `process_remark` text COMMENT '处理备注',
  `create_time` datetime(6) DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `update_time` datetime(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  PRIMARY KEY (`id`),
  KEY `idx_comment_id` (`comment_id`),
  KEY `idx_reporter_user_id` (`reporter_user_id`),
  KEY `idx_report_status` (`report_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论举报表';

-- 6. 更新现有数据，设置默认值
UPDATE `urb_feedback` SET 
  `comment_type` = 'FEEDBACK',
  `level` = 1,
  `reply_count` = 0
WHERE `comment_type` IS NULL;

-- 7. 添加外键约束（可选，根据实际需求决定是否启用）
-- ALTER TABLE `urb_feedback` 
-- ADD CONSTRAINT `fk_feedback_parent` FOREIGN KEY (`parent_id`) REFERENCES `urb_feedback` (`id`) ON DELETE CASCADE,
-- ADD CONSTRAINT `fk_feedback_reply_user` FOREIGN KEY (`reply_to_user_id`) REFERENCES `urb_user` (`id`) ON DELETE SET NULL;

-- ALTER TABLE `urb_comment_mention`
-- ADD CONSTRAINT `fk_mention_comment` FOREIGN KEY (`comment_id`) REFERENCES `urb_feedback` (`id`) ON DELETE CASCADE,
-- ADD CONSTRAINT `fk_mention_user` FOREIGN KEY (`mentioned_user_id`) REFERENCES `urb_user` (`id`) ON DELETE CASCADE;

-- ALTER TABLE `urb_comment_like`
-- ADD CONSTRAINT `fk_like_comment` FOREIGN KEY (`comment_id`) REFERENCES `urb_feedback` (`id`) ON DELETE CASCADE;

-- ALTER TABLE `urb_comment_report`
-- ADD CONSTRAINT `fk_report_comment` FOREIGN KEY (`comment_id`) REFERENCES `urb_feedback` (`id`) ON DELETE CASCADE,
-- ADD CONSTRAINT `fk_report_user` FOREIGN KEY (`reporter_user_id`) REFERENCES `urb_user` (`id`) ON DELETE CASCADE;
