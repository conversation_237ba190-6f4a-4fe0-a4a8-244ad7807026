package org.springblade.miniapp.controller.open;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.business.config.vo.UrbMenuVO;
import org.springblade.business.post.entity.Category;
import org.springblade.business.post.vo.CategoryVO;
import org.springblade.business.post.vo.FeedbackVO;
import org.springblade.common.anno.AccessLimit;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.business.post.vo.SupPostVO;
import org.springblade.business.config.entity.MiniappRegionConfig;
import org.springblade.miniapp.service.WeChatCategoryService;
import org.springblade.miniapp.service.WeChatFeedbackService;
import org.springblade.miniapp.service.WeChatPostService;
import org.springblade.miniapp.service.WeChatRegionService;
import org.springblade.miniapp.vo.MiniappRegionVO;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/blade-chat-open")
@io.swagger.v3.oas.annotations.tags.Tag(name = "小程序广告开放接口")
public class WeChatPostOpenController {
	private final org.springblade.miniapp.service.WeChatMenuService weChatMenuService;
	private final WeChatCategoryService categoryService;
	private final WeChatPostService postService;
	private final WeChatFeedbackService feedbackService;
	private final WeChatRegionService miniappRegionService;

	/**
	 * 分页查询
	 */
	@GetMapping("feedback/page")
	@AccessLimit(seconds = 30)
	@Operation(summary = "分页查询")
	public R<IPage<FeedbackVO>> page(FeedbackVO feedback, Query query) {
		IPage<FeedbackVO> pages = feedbackService.page(Condition.getPage(query), feedback);
		return R.data(pages);
	}
	/**
	 * 获取帖子列表
	 *
	 * 支持三种模式：
	 * P1模式（按时间排序）：传入 latitude 和 longitude，只计算距离但不按距离排序 --->最新帖子
	 * P2模式（按距离排序）：传入 searchLongitude 和 searchLatitude，按距离排序并限制范围   -->附件帖子
	 * P3模式（按距离排序）：传入 latitude 和 longitude 和 searchLongitude 和 searchLatitude，按在指定地点距离排序并限制范围   -->地图搜索帖子
	 */
	@GetMapping("/post/home-list")
	@Operation(summary = "获取帖子列表", description = "P1模式：传入latitude/longitude按时间排序；P2模式：传入searchLatitude/searchLatitude按距离排序")
	public R<IPage<SupPostVO>> getHomePostList(Query query, SupPostVO postVO) {
		return R.data(postService.getHomePostList(query, postVO));
	}

	@GetMapping("/config/menu")
	@Operation(summary = "获取菜单和轮播图", description = "返回菜单(category=0)和轮播图(category=1)，均按sort_weight升序排序")
	public R<Map<String, List<UrbMenuVO>>> getMenusAndBanners() {
		return R.data(weChatMenuService.getMenusAndBanners());
	}

	/**
	 * 获取分类列表  同步计算返回分类的已启动。已发布,审核通过的帖子总数
	 */
	@GetMapping("/categories")
	@Operation(summary = "获取分类列表", description = "获取所有启用的分类")
	public R<List<Category>> getCategories() {
		List<Category> categories = categoryService.getEnabledCategories();
		return R.data(categories);
	}

	/**
	 * 获取分类列表
	 */
	@AccessLimit(seconds = 30)
	@GetMapping("/config/category")
	@Operation(summary = "获取分类列表", description = "获取所有分类的分页列表")
	public R<IPage<CategoryVO>> getCategoryList(Query query) {
		return R.data(categoryService.getCategoryList(query));
	}

	/**
	 * 清理分类缓存
	 */
	@PostMapping("/config/category/clear-cache")
	@Operation(summary = "清理分类缓存", description = "清理分类相关的Redis缓存")
	public R<?> clearCategoryCache() {
		try {
			categoryService.clearCategoryCache();
			return R.success("缓存清理成功");
		} catch (Exception e) {
			log.error("清理分类缓存失败", e);
			return R.fail("缓存清理失败");
		}
	}
	/**
	 * 获取帖子详情
	 */
	@AccessLimit(seconds = 30)
	@GetMapping("/post/detail/{id}")
	@Operation(summary = "获取帖子详情")
	public R<SupPostVO> getPostDetail(@PathVariable("id") Long id) {
		try {
			SupPostVO post = postService.getPostDetail(id);
			if (post != null) {
				// 增加浏览次数
				return R.data(post);
			}
			return R.fail("帖子不存在");
		} catch (Exception e) {
			log.error("获取帖子详情失败", e);
			return R.fail("获取帖子详情失败");
		}
	}


	/**
	 * 获取开放的地区列表（用于前端展示）
	 */
	@GetMapping("/open-regions")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "获取开放地区", description = "获取开放的地区列表")
	public R<List<MiniappRegionVO>> getOpenRegions(@Parameter(description = "地区层级") @RequestParam(required = false) Integer level) {
		List<MiniappRegionVO> regions;
		if (level != null) {
			regions = miniappRegionService.getOpenRegionsByLevel(level);
		} else {
			// 获取所有开放的地区
			List<MiniappRegionConfig> configs = miniappRegionService.list(
				Wrappers.<MiniappRegionConfig>lambdaQuery()
					.eq(MiniappRegionConfig::getIsOpen, 1)
					.orderByAsc(MiniappRegionConfig::getLevel, MiniappRegionConfig::getSort)
			);
			regions = configs.stream()
				.map(config -> {
					MiniappRegionVO vo = new MiniappRegionVO();
					vo.setId(config.getId());
					vo.setRegionCode(config.getRegionCode());
					vo.setRegionName(config.getRegionName());
					vo.setParentCode(config.getParentCode());
					vo.setLevel(config.getLevel());
					vo.setSort(config.getSort());
					return vo;
				})
				.collect(java.util.stream.Collectors.toList());
		}
		return R.data(regions);
	}


	/**
	 * 根据父级编码查询子级开放地区配置
	 *
	 */
	@GetMapping("/list-by-parent")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "按父级查询", description = "根据父级编码查询子级地区配置")
	public R<List<MiniappRegionConfig>> listByParent(@Parameter(description = "父级编码", required = true) @RequestParam String parentCode) {
		List<MiniappRegionConfig> configs = miniappRegionService.list(
			Wrappers.<MiniappRegionConfig>lambdaQuery()
				.eq(MiniappRegionConfig::getParentCode, parentCode)
				.eq(MiniappRegionConfig::getIsOpen, 1)
				.orderByAsc(MiniappRegionConfig::getSort)
		);
		return R.data(configs);
	}


}
