package org.springblade.fileShare.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springblade.core.mp.base.BaseService;
import org.springblade.fileShare.dto.FileShareDTO;
import org.springblade.fileShare.entity.FileShare;
import org.springblade.fileShare.vo.FileShareVO;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;

/**
 * 文件分享服务接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface IFileShareService extends IService<FileShare> {

    /**
     * 上传文件并生成分享密钥
     *
     * @param file 上传的文件
     * @return 分享信息
     */
    FileShareVO uploadFile(MultipartFile file);

    /**
     * 分享文本内容并生成密钥
     *
     * @param dto 文本分享DTO
     * @return 分享信息
     */
    FileShareVO shareText(FileShareDTO dto);

    /**
     * 通过密钥获取分享信息
     *
     * @param shareKey 分享密钥
     * @return 分享信息
     */
    FileShareVO getByShareKey(String shareKey);

    /**
     * 通过密钥下载文件
     *
     * @param shareKey 分享密钥
     * @param response HTTP响应
     */
    void downloadByShareKey(String shareKey, HttpServletResponse response);

    /**
     * 通过密钥获取文本内容
     *
     * @param shareKey 分享密钥
     * @return 文本内容
     */
    String getTextByShareKey(String shareKey);

    /**
     * 生成6位随机密钥
     *
     * @return 6位随机密钥
     */
    String generateShareKey();
}
