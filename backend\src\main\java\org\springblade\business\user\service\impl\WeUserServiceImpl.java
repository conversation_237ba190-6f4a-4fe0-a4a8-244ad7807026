/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<PERSON><PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qiniu.util.Auth;
import lombok.AllArgsConstructor;
import org.springblade.business.statistics.entity.UserRequestStats;
import org.springblade.business.user.entity.WeUser;
import org.springblade.business.user.mapper.WeUserMapper;
import org.springblade.business.user.service.IWeUserService;
import org.springblade.business.user.service.RedisRequestStatsService;
import org.springblade.business.user.vo.WeUserVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.modules.system.vo.UserVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 用户信息 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Service
@AllArgsConstructor
public class WeUserServiceImpl extends BaseServiceImpl<WeUserMapper, WeUser> implements IWeUserService {

	private final RedisRequestStatsService redisRequestStatsService;
	@Override
	public IPage<WeUserVO> selectUserPage(IPage<WeUserVO> page, WeUserVO user) {
		return page.setRecords(baseMapper.selectUserPage(page, user));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void createNewUser(Long id, String tenantId) {
		WeUser weUser = new WeUser();
		weUser.setId(id);
		weUser.setNickname("用户"+ RandomUtil.randomString(6));
		weUser.setGender("0");
		save(weUser);
	}

	@Override
	public List<Map<String, Object>> getUserRequestStats(String startDate, String endDate) {
		List<Map<String, Object> > list = baseMapper.getUserActiveCount(startDate, endDate);
		if(endDate.equals(LocalDate.now().toString())){
			Long activeUserCount =redisRequestStatsService.getActiveUserCount(LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE));
			Map<String, Object> map = Map.of("date", LocalDate.now(), "count", activeUserCount);
			list.add( map);
		}
		return list;
	}

	@Override
	public List<UserRequestStats> getUserRequestStatsTop20(String date) {
		return baseMapper.getUserRequestStatsTop20(date);
	}

	@Override
	public WeUserVO getDetail(WeUserVO user) {
		//设置分页参数
		Page<WeUserVO> page = Page.of(1, 1);
		List<WeUserVO> list =baseMapper.selectUserPage(page, user);
		return !list.isEmpty()?list.get(0):null;
	}

    @Override
    public Integer getPoints(Long userId) {
		WeUser user = this.getById(userId);
		return user.getBalance();
    }
}
