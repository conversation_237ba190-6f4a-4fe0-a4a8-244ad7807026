package org.springblade.fileShare.service.impl;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.Random;

import org.springblade.fileShare.dto.FileShareDTO;
import org.springblade.fileShare.entity.FileShare;
import org.springblade.fileShare.mapper.FileShareMapper;
import org.springblade.fileShare.service.IFileShareService;
import org.springblade.fileShare.vo.FileShareVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 文件分享服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileShareServiceImpl extends ServiceImpl<FileShareMapper, FileShare> implements IFileShareService {

    private static final String UPLOAD_DIR = "uploads/fileshare/";
    private static final String SHARE_TYPE_FILE = "FILE";
    private static final String SHARE_TYPE_TEXT = "TEXT";
    private static final int SHARE_KEY_LENGTH = 6;
    private static final int EXPIRE_DAYS = 1;

    @Override
    public FileShareVO uploadFile(MultipartFile file) {
        try {
            // 检查文件是否为空
            if (file.isEmpty()) {
                throw new RuntimeException("上传的文件不能为空");
            }

            // 创建上传目录
            Path uploadPath = Paths.get(UPLOAD_DIR);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
            }

            // 生成唯一文件名
            String originalFilename = file.getOriginalFilename();
            String fileExtension = "";
            if (StrUtil.isNotBlank(originalFilename) && originalFilename.contains(".")) {
                fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }
            String fileName = IdUtil.fastSimpleUUID() + fileExtension;

            // 保存文件
            Path filePath = uploadPath.resolve(fileName);
            Files.copy(file.getInputStream(), filePath);

            // 创建文件分享记录
            FileShare fileShare = new FileShare();
            fileShare.setShareKey(generateShareKey());
            fileShare.setShareType(SHARE_TYPE_FILE);
            fileShare.setFileName(originalFilename);
            fileShare.setFilePath(filePath.toString());
            fileShare.setFileSize(file.getSize());
            fileShare.setFileType(file.getContentType());
            fileShare.setExpireTime(LocalDateTime.now().plusDays(EXPIRE_DAYS));
            fileShare.setDownloadCount(0);
            fileShare.setStatus(0);

            // 保存到数据库
            save(fileShare);

            // 转换为VO返回
            return convertToVO(fileShare);
        } catch (IOException e) {
            log.error("文件上传失败", e);
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }

    @Override
    public FileShareVO shareText(FileShareDTO dto) {
        // 验证文本内容
        if (StrUtil.isBlank(dto.getTextContent())) {
            throw new RuntimeException("文本内容不能为空");
        }

        // 创建文本分享记录
        FileShare fileShare = new FileShare();
        fileShare.setShareKey(generateShareKey());
        fileShare.setShareType(SHARE_TYPE_TEXT);
        fileShare.setTextContent(dto.getTextContent());
        fileShare.setExpireTime(LocalDateTime.now().plusDays(EXPIRE_DAYS));
        fileShare.setDownloadCount(0);
        fileShare.setStatus(0);

        // 保存到数据库
        save(fileShare);

        // 转换为VO返回
        return convertToVO(fileShare);
    }

    @Override
    public FileShareVO getByShareKey(String shareKey) {
        if (StrUtil.isBlank(shareKey)) {
            throw new RuntimeException("分享密钥不能为空");
        }

        // 查询分享记录
        LambdaQueryWrapper<FileShare> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileShare::getShareKey, shareKey);
        FileShare fileShare = getOne(queryWrapper);

        if (fileShare == null) {
            throw new RuntimeException("分享记录不存在");
        }

        // 检查是否过期
        if (LocalDateTime.now().isAfter(fileShare.getExpireTime())) {
            fileShare.setStatus(1); // 标记为已过期
            updateById(fileShare);
            throw new RuntimeException("分享已过期");
        }

        // 检查状态
        if (fileShare.getStatus() != 0) {
            throw new RuntimeException("分享已被删除或已过期");
        }

        return convertToVO(fileShare);
    }

    @Override
    public void downloadByShareKey(String shareKey, HttpServletResponse response) {
        FileShareVO fileShareVO = getByShareKey(shareKey);

        if (!SHARE_TYPE_FILE.equals(fileShareVO.getShareType())) {
            throw new RuntimeException("该分享不是文件类型");
        }

        // 查询完整的文件信息
        LambdaQueryWrapper<FileShare> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileShare::getShareKey, shareKey);
        FileShare fileShare = getOne(queryWrapper);

        File file = new File(fileShare.getFilePath());
        if (!file.exists()) {
            throw new RuntimeException("文件不存在");
        }

        try {
            // 设置响应头
            response.setContentType(fileShare.getFileType());
            response.setHeader("Content-Disposition", "attachment; filename=\"" +
                    new String(fileShare.getFileName().getBytes("UTF-8"), "ISO-8859-1") + "\"");
            response.setContentLengthLong(fileShare.getFileSize());

            // 写入文件内容
            FileUtil.writeToStream(file, response.getOutputStream());

            // 更新下载次数
            fileShare.setDownloadCount(fileShare.getDownloadCount() + 1);
            updateById(fileShare);
        } catch (IOException e) {
            log.error("文件下载失败", e);
            throw new RuntimeException("文件下载失败: " + e.getMessage());
        }
    }

    @Override
    public String getTextByShareKey(String shareKey) {
        FileShareVO fileShareVO = getByShareKey(shareKey);

        if (!SHARE_TYPE_TEXT.equals(fileShareVO.getShareType())) {
            throw new RuntimeException("该分享不是文本类型");
        }

        // 查询完整的文件信息
        LambdaQueryWrapper<FileShare> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileShare::getShareKey, shareKey);
        FileShare fileShare = getOne(queryWrapper);

        // 更新下载次数
        fileShare.setDownloadCount(fileShare.getDownloadCount() + 1);
        updateById(fileShare);

        return fileShare.getTextContent();
    }

    @Override
    public String generateShareKey() {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random random = new Random();
        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < SHARE_KEY_LENGTH; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }

        String shareKey = sb.toString();

        // 检查密钥是否已存在
        LambdaQueryWrapper<FileShare> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileShare::getShareKey, shareKey);
        if (count(queryWrapper) > 0) {
            // 如果存在，递归生成新的密钥
            return generateShareKey();
        }

        return shareKey;
    }

    /**
     * 转换为VO对象
     */
    private FileShareVO convertToVO(FileShare fileShare) {
        FileShareVO vo = new FileShareVO();
        BeanUtils.copyProperties(fileShare, vo);
        return vo;
    }
}
