package org.springblade.miniapp.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import org.springblade.business.group.vo.GroupCategoryVO;
import org.springblade.business.group.vo.GroupInfoVO;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.miniapp.service.WechatGroupService;
import org.springframework.web.bind.annotation.*;

@RestController
@AllArgsConstructor
@RequestMapping("/blade-chat/group")
@io.swagger.v3.oas.annotations.tags.Tag(name = "群管理")
public class WeChatGroupController {


	private final WechatGroupService groupService;

	/**
	 * 自定义分页 群分类表
	 */
	@GetMapping("/category/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入groupCategory")
	public R<IPage<GroupCategoryVO>> page(GroupCategoryVO groupCategory, Query query) {
		IPage<GroupCategoryVO> pages = groupService.selectGroupCategoryPage(Condition.getPage(query), groupCategory);
		return R.data(pages);
	}


	/**
	 * 自定义分页 群信息表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入groupInfo")
	public R<IPage<GroupInfoVO>> page(GroupInfoVO groupInfo, Query query) {
		IPage<GroupInfoVO> pages = groupService.selectGroupInfoPage(Condition.getPage(query), groupInfo);
		return R.data(pages);
	}

}
