<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.system.mapper.UserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="userResultMap" type="org.springblade.modules.system.entity.User">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="code" property="code"/>
        <result column="account" property="account"/>
        <result column="password" property="password"/>
        <result column="name" property="name"/>
        <result column="real_name" property="realName"/>
        <result column="email" property="email"/>
        <result column="phone" property="phone"/>
        <result column="birthday" property="birthday"/>
        <result column="sex" property="sex"/>
        <result column="role_id" property="roleId"/>
        <result column="dept_id" property="deptId"/>
        <result column="post_id" property="postId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="baseColumnList">
        select id,
        create_user AS createUser,
        create_time AS createTime,
        update_user AS updateUser,
        update_time AS updateTime,
        status,
        is_deleted AS isDeleted,
        account, password, name, real_name, email, phone, birthday, sex, role_id, dept_id
    </sql>

    <select id="selectUserPage" resultMap="userResultMap">
        select * from blade_user where is_deleted = 0
    </select>

    <select id="getUser" resultMap="userResultMap">
        SELECT
            *
        FROM
            blade_user
        WHERE
            tenant_id = #{param1} and account = #{param2} and password = #{param3} and is_deleted = 0
    </select>

    <select id="getRoleName" resultType="java.lang.String">
        SELECT
        role_name
        FROM
        blade_role
        WHERE
        id IN
        <foreach collection="array" item="ids" index="index" open="(" close=")" separator=",">
            #{ids}
        </foreach>
        and is_deleted = 0
    </select>

    <select id="getRoleAlias" resultType="java.lang.String">
        SELECT
            role_alias
        FROM
            blade_role
        WHERE
            id IN
        <foreach collection="array" item="ids" index="index" open="(" close=")" separator=",">
            #{ids}
        </foreach>
        and is_deleted = 0
    </select>

    <select id="getDeptName" resultType="java.lang.String">
        SELECT
            dept_name
        FROM
            blade_dept
        WHERE
            id IN
        <foreach collection="array" item="ids" index="index" open="(" close=")" separator=",">
            #{ids}
        </foreach>
        and is_deleted = 0
    </select>

    <select id="exportUser" resultType="org.springblade.modules.system.excel.UserExcel">
        SELECT id, tenant_id, account, name, real_name, email, phone, birthday, role_id, dept_id, post_id FROM blade_user ${ew.customSqlSegment}
    </select>

</mapper>
