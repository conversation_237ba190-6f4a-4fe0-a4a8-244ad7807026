#数据源配置
spring:
  data:
    redis:
      ##redis 单机环境配置
      host: 127.0.0.1
      port: 6379
      password:
      database: 0
      ssl:
        enabled: false
      ##redis 集群环境配置
      #cluster:
      #  nodes: 127.0.0.1:7001,127.0.0.1:7002,127.0.0.1:7003
      #  commandTimeout: 5000
  datasource:
    url: *****************************************************************************************************************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver

#第三方登陆
social:
  enabled: true
  domain: http://127.0.0.1:1888

#blade配置
blade:
  prop:
    upload-domain: http://localhost:8888
    remote-mode: true
    remote-path: /usr/share/nginx/html
    remote-path-iis: D://iis/html
    log-mode: true
