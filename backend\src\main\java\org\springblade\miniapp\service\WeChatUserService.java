package org.springblade.miniapp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.business.post.vo.FeedbackVO;
import org.springblade.business.post.vo.SupPostVO;
import org.springblade.business.user.dto.WeUserDTO;
import org.springblade.business.user.vo.WeUserVO;
import org.springblade.core.mp.support.Query;
import org.springblade.miniapp.vo.UserProfile;

import java.util.Map;

public interface WeChatUserService {
	UserProfile getUserInfo(Long id);

	boolean updateUserInfo(UserProfile userProfile);

	/**
	 * 获取用户统计数据
	 *
	 * @return Map 包含帖子数、点赞数、收藏数
	 */
	Map<String, Object> getUserStats();

	IPage<SupPostVO> getPostListByUserId(Long userId, Query query);

	IPage<FeedbackVO> getFeedbackList(Long userId, Query query);

	Integer getPoints(Long userId);
}
