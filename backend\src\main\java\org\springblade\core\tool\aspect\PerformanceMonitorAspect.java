package org.springblade.core.tool.aspect;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 性能监控切面
 * 用于监控方法执行时间和性能指标
 *
 * <AUTHOR>
 * @version 1.0
 * @email <EMAIL>
 * @website duofan.top
 * @date 2025/8/1
 */
@Aspect
@Component
@Slf4j
public class PerformanceMonitorAspect {

    /**
     * 性能监控注解
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    public @interface PerformanceMonitor {
        /**
         * 操作描述
         */
        String value() default "";
        
        /**
         * 慢查询阈值（毫秒）
         */
        long slowThreshold() default 1000;
        
        /**
         * 是否记录参数
         */
        boolean logParams() default false;
        
        /**
         * 是否记录返回值
         */
        boolean logResult() default false;
    }

    /**
     * 定义切点 - 所有Service层方法
     */
    @Pointcut("execution(* org.springblade..service..*(..))")
    public void serviceLayer() {}

    /**
     * 定义切点 - 带有PerformanceMonitor注解的方法
     */
    @Pointcut("@annotation(performanceMonitor)")
    public void annotatedMethod(PerformanceMonitor performanceMonitor) {}

    /**
     * 监控Service层方法执行时间
     */
    @Around("serviceLayer()")
    public Object monitorServiceMethod(ProceedingJoinPoint joinPoint) throws Throwable {
        return monitorExecution(joinPoint, null, 1000, false, false);
    }

    /**
     * 监控带注解的方法
     */
    @Around("annotatedMethod(performanceMonitor)")
    public Object monitorAnnotatedMethod(ProceedingJoinPoint joinPoint, PerformanceMonitor performanceMonitor) throws Throwable {
        return monitorExecution(
            joinPoint, 
            performanceMonitor.value(), 
            performanceMonitor.slowThreshold(),
            performanceMonitor.logParams(),
            performanceMonitor.logResult()
        );
    }

    /**
     * 执行监控逻辑
     */
    private Object monitorExecution(ProceedingJoinPoint joinPoint, String description, 
                                  long slowThreshold, boolean logParams, boolean logResult) throws Throwable {
        
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        String fullMethodName = className + "." + methodName;
        
        if (description == null || description.isEmpty()) {
            description = fullMethodName;
        }

        StopWatch stopWatch = new StopWatch(description);
        stopWatch.start();

        // 记录方法开始
        if (log.isDebugEnabled()) {
            if (logParams) {
                log.debug("开始执行方法: {}, 参数: {}", description, joinPoint.getArgs());
            } else {
                log.debug("开始执行方法: {}", description);
            }
        }

        Object result = null;
        Throwable exception = null;

        try {
            // 执行目标方法
            result = joinPoint.proceed();
            return result;
        } catch (Throwable e) {
            exception = e;
            throw e;
        } finally {
            stopWatch.stop();
            long executionTime = stopWatch.getTotalTimeMillis();

            // 记录执行结果
            logExecutionResult(description, executionTime, slowThreshold, result, exception, logResult);
            
            // 记录性能指标
            recordPerformanceMetrics(fullMethodName, executionTime, exception != null);
        }
    }

    /**
     * 记录执行结果
     */
    private void logExecutionResult(String description, long executionTime, long slowThreshold, 
                                  Object result, Throwable exception, boolean logResult) {
        
        if (exception != null) {
            // 方法执行异常
            log.error("方法执行异常: {}, 耗时: {}ms", description, executionTime, exception);
        } else if (executionTime > slowThreshold) {
            // 慢方法警告
            if (logResult && result != null) {
                log.warn("慢方法警告: {}, 耗时: {}ms, 返回值: {}", description, executionTime, result);
            } else {
                log.warn("慢方法警告: {}, 耗时: {}ms", description, executionTime);
            }
        } else if (log.isDebugEnabled()) {
            // 正常执行
            if (logResult && result != null) {
                log.debug("方法执行完成: {}, 耗时: {}ms, 返回值: {}", description, executionTime, result);
            } else {
                log.debug("方法执行完成: {}, 耗时: {}ms", description, executionTime);
            }
        }
    }

    /**
     * 记录性能指标（可以扩展为发送到监控系统）
     */
    private void recordPerformanceMetrics(String methodName, long executionTime, boolean hasException) {
        // 这里可以集成监控系统，如Micrometer、Prometheus等
        // 示例：记录方法执行时间分布
        if (log.isTraceEnabled()) {
            log.trace("性能指标 - 方法: {}, 执行时间: {}ms, 是否异常: {}", 
                     methodName, executionTime, hasException);
        }
        
        // TODO: 集成监控系统
        // meterRegistry.timer("method.execution.time", "method", methodName)
        //     .record(executionTime, TimeUnit.MILLISECONDS);
    }

    /**
     * 内存使用监控切面
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    public @interface MemoryMonitor {
        String value() default "";
    }

    /**
     * 监控内存使用情况
     */
    @Around("@annotation(memoryMonitor)")
    public Object monitorMemoryUsage(ProceedingJoinPoint joinPoint, MemoryMonitor memoryMonitor) throws Throwable {
        String description = memoryMonitor.value();
        if (description.isEmpty()) {
            description = joinPoint.getSignature().getName();
        }

        // 记录执行前内存状态
        Runtime runtime = Runtime.getRuntime();
        long beforeMemory = runtime.totalMemory() - runtime.freeMemory();

        try {
            Object result = joinPoint.proceed();
            
            // 记录执行后内存状态
            long afterMemory = runtime.totalMemory() - runtime.freeMemory();
            long memoryDiff = afterMemory - beforeMemory;
            
            if (log.isDebugEnabled()) {
                log.debug("内存使用监控 [{}] - 执行前: {}MB, 执行后: {}MB, 差值: {}MB",
                         description,
                         beforeMemory / 1024 / 1024,
                         afterMemory / 1024 / 1024,
                         memoryDiff / 1024 / 1024);
            }
            
            return result;
        } catch (Throwable e) {
            log.error("方法执行异常，内存监控中断: {}", description, e);
            throw e;
        }
    }

    /**
     * 数据库查询监控注解
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    public @interface DatabaseMonitor {
        String value() default "";
        int maxQueries() default 10; // 最大查询次数阈值
    }

    /**
     * 数据库查询监控（需要配合数据库连接池或ORM框架）
     */
    @Around("@annotation(databaseMonitor)")
    public Object monitorDatabaseQueries(ProceedingJoinPoint joinPoint, DatabaseMonitor databaseMonitor) throws Throwable {
        String description = databaseMonitor.value();
        if (description.isEmpty()) {
            description = joinPoint.getSignature().getName();
        }

        // TODO: 实现数据库查询次数统计
        // 这里需要配合数据库连接池或ORM框架来统计查询次数
        
        try {
            Object result = joinPoint.proceed();
            
            // TODO: 检查查询次数是否超过阈值
            // if (queryCount > databaseMonitor.maxQueries()) {
            //     log.warn("数据库查询次数过多 [{}] - 查询次数: {}, 阈值: {}", 
            //              description, queryCount, databaseMonitor.maxQueries());
            // }
            
            return result;
        } catch (Throwable e) {
            log.error("数据库操作异常: {}", description, e);
            throw e;
        }
    }
}
