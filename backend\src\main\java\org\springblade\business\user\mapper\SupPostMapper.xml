<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.user.mapper.SupPostMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="postResultMap" type="org.springblade.business.post.vo.SupPostVO">
        <id column="id" property="id"/>
        <result column="content" property="content"/>
        <result column="images" property="images"/>
        <result column="category_id" property="categoryId"/>
        <result column="tags" property="tags"/>
        <result column="contact_name" property="contactName"/>
        <result column="contact_phone" property="contactPhone"/>
        <result column="contact_type" property="contactType"/>
        <result column="publish_time" jdbcType="TIMESTAMP" property="publishTime"/>
        <result column="publish_status" property="publishStatus"/>

        <result column="status" property="status"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>


        <result column="top" property="top"/>
        <result column="completed" property="completed"/>
        <result column="distance" property="distance"/>
        <result column="mapDistance" property="mapDistance"/>
        <result column="businessType" property="businessType"/>
        <result column="institution_id" property="institutionId"/>



        <!--  位置信息-->
        <result column="location" property="location"/>
        <result column="address" property="address"/>
        <result column="latitude" property="latitude"/>
        <result column="longitude" property="longitude"/>


        <!-- 分类信息 -->
        <association property="category" javaType="org.springblade.business.post.entity.Category">
            <result column="category_name" property="name"/>
            <result column="category_id" property="id"/>
        </association>

        <!-- 帖子状态统计信息 -->
        <association property="stats" javaType="org.springblade.business.post.dto.PostStatsDTO">
            <result column="is_liked" property="isLiked"/>
            <result column="is_favorite" property="isFavorite"/>
            <result column="view_count" property="viewCount"/>
            <result column="like_count" property="likeCount"/>
            <result column="feedback_count" property="feedbackCount"/>
            <result column="favorite_count" property="favoriteCount"/>
        </association>

        <!-- 工作招聘信息映射 -->
        <association property="jobOffer" javaType="org.springblade.business.post.entity.JobOffer">
            <id column="job_offer_id" property="id"/>
            <result column="job_offer_post_id" property="postId"/>
            <result column="job_offer_recruit_type" property="recruitType"/>
            <result column="job_offer_workplace" property="workplace"/>
            <result column="job_offer_job_title" property="jobTitle"/>
            <result column="job_offer_headcount" property="headcount"/>
            <result column="job_offer_job_keywords" property="jobKeywords"/>
            <result column="job_offer_job_description" property="jobDescription"/>
        </association>

        <!-- 求职信息映射 -->
        <association property="jobSeeking" javaType="org.springblade.business.post.entity.JobSeeking">
            <id column="job_seeking_id" property="id"/>
            <result column="job_seeking_post_id" property="postId"/>
            <result column="job_seeking_user_id" property="userId"/>
            <result column="job_seeking_type" property="seekingType"/>
            <result column="job_seeking_expected_position" property="expectedPosition"/>
            <result column="job_seeking_salary_expectation" property="salaryExpectation"/>
            <result column="job_seeking_position_preference" property="positionPreference"/>
        </association>

<!--        用户信息-->
        <association property="user" javaType="org.springblade.business.user.entity.WeUser">
            <result column="userNickname" property="nickname"/>
            <result column="userMobile" property="mobile"/>
            <result column="userAvatar" property="avatar"/>
            <result column="userEmail" property="email"/>
            <result column="userGender" property="gender"/>
            <result column="userAge" property="age"/>
            <result column="userSignature" property="signature"/>
        </association>

    </resultMap>
    <sql id="post_info_join">
        LEFT JOIN urb_user u ON p.create_user = u.id
        LEFT JOIN (
            SELECT relevancy_id, COUNT(1) as view_count
            FROM urb_view_log
            where type = '0'
            and is_deleted = 0
            GROUP BY relevancy_id
        ) v ON p.id = v.relevancy_id
        LEFT JOIN (
            SELECT relevancy_id, COUNT(1) as like_count
            FROM urb_like
            where is_deleted = 0
            AND type = '0'
            GROUP BY relevancy_id
        ) l ON p.id = l.relevancy_id
        LEFT JOIN (
            SELECT relevancy_id, COUNT(1) as favorite_count
            FROM urb_favorite
            where is_deleted = 0
            AND type = '0'
            GROUP BY relevancy_id
        ) f ON p.id = f.relevancy_id
        LEFT JOIN (
            SELECT relevancy_id, COUNT(1) as feedback_count
            FROM urb_feedback
            where is_deleted = 0
            AND type = '0'
            GROUP BY relevancy_id
        ) fd ON p.id = fd.relevancy_id
        LEFT JOIN urb_category c ON p.category_id = c.id

    </sql>
    <!-- 查状态直返 -->
    <sql id="post_info_select">
        u.nickname,
        u.avatar,
        COALESCE(v.view_count, 0) as view_count,
        COALESCE(l.like_count, 0) as like_count,
        COALESCE(fd.feedback_count, 0) as feedback_count,
        COALESCE(f.favorite_count, 0) as favorite_count,
        c.name as category_name
    </sql>
    <sql id="post_info_state_select">
CASE
    WHEN EXISTS (
        SELECT 1
        FROM urb_like
        WHERE relevancy_id = p.id
        AND type = '0'
          AND user_id = #{model.visitUser}
          AND is_deleted = 0
        )
        THEN 1
        ELSE 0
        END AS is_liked,

-- 判断当前用户是否收藏该帖子（1=已收藏，0=未收藏）
CASE
    WHEN EXISTS (
        SELECT 1
        FROM urb_favorite
        WHERE relevancy_id = p.id
        AND type = '0'
          AND user_id = #{model.visitUser}
          AND is_deleted = 0
        )
        THEN 1
        ELSE 0
        END AS is_favorite
    </sql>
    <!-- 获取帖子详情 -->
    <select id="selectPostDetail" resultMap="postResultMap">
        SELECT p.*,
        --             顺风车字段
        cpool.id AS carpool_id,
        cpool.region as carpool_region,
        cpool.carpool_type as carpool_carpool_type,
        cpool.departure as carpool_departure,
        cpool.destination as carpool_destination,
        cpool.via as carpool_via,
        cpool.departure_time as carpool_departure_time,
        cpool.empty_seats as carpool_empty_seats,
        cpool.tonnage as carpool_tonnage,
        cpool.post_id AS carpool_post_id,

        -- 招人字段
        jobOffer.id AS job_offer_id,
        jobOffer.post_id AS job_offer_post_id,
        jobOffer.recruit_type AS job_offer_recruit_type,
        jobOffer.workplace AS job_offer_workplace,
        jobOffer.job_title AS job_offer_job_title,
        jobOffer.headcount AS job_offer_headcount,
        jobOffer.salary AS job_offer_salary,
        jobOffer.job_keywords AS job_offer_job_keywords,
        jobOffer.job_description AS job_offer_job_description,

        -- 求职字段
        jobSeeking.id AS job_seeking_id,
        jobSeeking.post_id AS job_seeking_post_id,
        jobSeeking.user_id AS job_seeking_user_id,
        jobSeeking.seeking_type AS job_seeking_type,
        jobSeeking.expected_position AS job_seeking_expected_position,
        jobSeeking.salary_expectation AS job_seeking_salary_expectation,
        jobSeeking.position_preference AS job_seeking_position_preference,

        -- 用户信息
        u.nickname AS userNickname,
        u.mobile AS userMobile,
        u.avatar AS userAvatar,
        u.email AS userEmail,
        u.gender AS userGender,
        u.age AS userAge,
        u.signature AS userSignature,

        <include refid="post_info_select"/>
        ,
        <include refid="post_info_state_select"></include>
        FROM urb_post p
        LEFT JOIN urb_post_carpool cpool ON p.id = cpool.post_id
        LEFT JOIN urb_job_seeking jobSeeking ON p.id = jobSeeking.post_id
        LEFT JOIN urb_job_offer jobOffer ON p.id = jobOffer.post_id
        <include refid="post_info_join"/>
        WHERE p.id = #{model.id}
        AND p.is_deleted = 0
    </select>

    <!-- 检查用户是否点赞过帖子 -->
    <select id="checkUserLiked" resultType="int">
        SELECT COUNT(1)
        FROM urb_like
        WHERE relevancy_id = #{model.postId}
          AND type = '0'
          AND user_id = #{model.createUser}
          AND is_deleted = 0
    </select>

    <!-- 检查用户是否收藏过帖子 -->
    <select id="checkUserFavorited" resultType="int">
        SELECT COUNT(1)
        FROM urb_favorite
        WHERE relevancy_id = #{model.postId}
          AND type = '0'
          AND user_id = #{model.createUser}
          AND is_deleted = 0
    </select>

    <select id="selectPostPage" resultMap="postResultMap">
        SELECT
        p.*,
        <include refid="post_info_select"/>
        FROM urb_post p
        <include refid="post_info_join"/>
        WHERE 1 = 1
        <if test="post.createUser!=null">
            AND p.create_user = #{post.createUser}
        </if>
        <if test="post.publishStatus!=null and post.publishStatus!=''">
            AND p.publish_status = #{post.publishStatus}
        </if>
        <if test="post.auditStatus!=null and post.auditStatus!=''">
            AND p.audit_status = #{post.auditStatus}
        </if>
        AND p.is_deleted = 0
--         TODO 动态排序
        ORDER BY p.create_time DESC
    </select>

    <!-- 查询我的帖子 -->
    <select id="selectMyPosts" resultMap="postResultMap">
        SELECT
        p.*,
        <include refid="post_info_select"/>,
        <include refid="post_info_state_select"/>
        FROM urb_post p
        <include refid="post_info_join"/>
        WHERE p.create_user = #{model.createUser}
        AND p.is_deleted = 0
        ORDER BY p.create_time DESC
    </select>


    <!-- 查询用户浏览记录 -->
    <select id="selectViewHistory" resultType="org.springblade.business.post.vo.SupPostVO">
        SELECT p.*,
               vh.create_time as view_time,
               u.nickname,
               u.avatar
        FROM urb_post p
                 INNER JOIN urb_view_log vh ON p.id = vh.relevancy_id
                 LEFT JOIN blade_user u ON p.create_user = u.id
        WHERE vh.user_id = #{model.createUser}
          AND vh.is_deleted = 0
        ORDER BY vh.create_time DESC
    </select>

    <!-- 清空用户帖子浏览记录 -->
    <update id="clearViewHistory">
        UPDATE
        urb_view_log
        SET is_deleted  = 1,
        WHERE user_id = #{model.createUser}
        AND type = '0'
    </update>


    <!-- 查询拨号记录 -->
    <select id="selectCallHistory" resultType="org.springblade.business.post.vo.SupPostVO">
        SELECT p.*,
               cl.name        as contact_name,
               cl.phone       as contact_phone,
               cl.create_time as call_time
        FROM urb_post p
                 INNER JOIN urb_call_log cl ON p.id = cl.post_id
                 LEFT JOIN urb_contact c ON p.id = c.id
                 LEFT JOIN blade_user u ON p.create_user = u.id
        WHERE cl.user_id = #{model.createUser}
          AND cl.is_deleted = 0
        ORDER BY cl.create_time DESC
    </select>

    <!-- 清空拨号记录 -->
    <update id="clearCallHistory">
        UPDATE urb_call_log
        SET is_deleted  = 1,
            update_time = NOW(),
            update_user = #{model.createUser}
        WHERE user_id = #{model.createUser}
          AND is_deleted = 0
    </update>

    <!-- 添加拨号记录 -->
    <insert id="insertCallHistory">
        INSERT INTO urb_call_log (post_id,
                                  user_id,
                                  create_time,
                                  create_user,
                                  status,
                                  is_deleted)
        VALUES (#{model.postId},
                #{model.createUser},
                NOW(),
                #{model.createUser},
                1,
                0)
        ON DUPLICATE KEY UPDATE create_time = NOW(),
                                update_time = NOW(),
                                update_user = #{model.createUser},
                                is_deleted  = 0
    </insert>
    <insert id="savePostCategory">
        INSERT INTO urb_post_category (post_id, category_id)
        VALUES (#{model.postId}, #{model.categoryId})
    </insert>

    <!-- 查询草稿列表 -->
    <select id="selectDraftList" resultType="org.springblade.business.post.vo.SupPostVO">
        SELECT p.*,
               c.name  as contact_name,
               c.phone as contact_phone
        FROM urb_post p
                 LEFT JOIN blade_user u ON p.create_user = u.id
                 LEFT JOIN urb_contact c ON p.id = c.id
        WHERE p.create_user = #{model.createUser}
          AND p.status = 0
          AND p.is_deleted = 0
        ORDER BY p.create_time DESC
    </select>

    <!-- 查询草稿详情 -->
    <select id="selectDraftDetail" resultType="org.springblade.business.post.vo.SupPostVO">
        SELECT p.*,
               c.name  as contact_name,
               c.phone as contact_phone
        FROM urb_post p
                 LEFT JOIN blade_user u ON p.create_user = u.id
                 LEFT JOIN urb_contact c ON p.id = c.id
        WHERE p.id = #{model.id}
          AND p.create_user = #{model.createUser}
          AND p.status = 0
          AND p.is_deleted = 0
    </select>

    <!-- 删除草稿 -->
    <update id="replyDraft">
        UPDATE urb_post
        SET is_deleted  = 0,
            update_time = NOW(),
            update_user = #{model.createUser}
        WHERE id = #{model.id}
          AND create_user = #{model.createUser}
          AND status = 0
    </update>

    <!-- 查询帖子列表 -->
    <select id="selectPostList" resultMap="postResultMap">
        SELECT
        p.*,
        p.business_type as businessType,
        <if test="(model.sortMode == 'distance' and model.latitude != null and model.longitude != null) or (model.sortMode == 'time' and model.latitude != null and model.longitude != null)">
            (
            6371 * acos(
            cos(radians(#{model.latitude}))
            * cos(radians(p.latitude))
            * cos(radians(p.longitude) - radians(#{model.longitude}))
            + sin(radians(#{model.latitude})) * sin(radians(p.latitude))
            )
            ) AS distance,
        </if>
        <!-- P3模式：同时计算用户位置距离 -->
        <if test="model.queryMode == 'P3' and model.userLatitude != null and model.userLongitude != null">
            (
            6371 * acos(
            cos(radians(#{model.userLatitude}))
            * cos(radians(p.latitude))
            * cos(radians(p.longitude) - radians(#{model.userLongitude}))
            + sin(radians(#{model.userLatitude})) * sin(radians(p.latitude))
            )
            ) AS mapDistance,
        </if>
        --             顺风车字段
        cpool.id AS carpool_id,
        cpool.region as carpool_region,
        cpool.carpool_type as carpool_carpool_type,
        cpool.departure as carpool_departure,
        cpool.destination as carpool_destination,
        cpool.via as carpool_via,
        cpool.departure_time as carpool_departure_time,
        cpool.empty_seats as carpool_empty_seats,
        cpool.tonnage as carpool_tonnage,
        cpool.post_id AS carpool_post_id,

        -- 招人字段
        jobOffer.id AS job_offer_id,
        jobOffer.post_id AS job_offer_post_id,
        jobOffer.recruit_type AS job_offer_recruit_type,
        jobOffer.workplace AS job_offer_workplace,
        jobOffer.job_title AS job_offer_job_title,
        jobOffer.headcount AS job_offer_headcount,
        jobOffer.salary AS job_offer_salary,
        jobOffer.job_keywords AS job_offer_job_keywords,
        jobOffer.job_description AS job_offer_job_description,

        -- 求职字段
        jobSeeking.id AS job_seeking_id,
        jobSeeking.post_id AS job_seeking_post_id,
        jobSeeking.user_id AS job_seeking_user_id,
        jobSeeking.seeking_type AS job_seeking_type,
        jobSeeking.expected_position AS job_seeking_expected_position,
        jobSeeking.salary_expectation AS job_seeking_salary_expectation,
        jobSeeking.position_preference AS job_seeking_position_preference,

        -- 用户信息
        u.nickname AS userNickname,
        u.mobile AS userMobile,
        u.avatar AS userAvatar,
        u.email AS userEmail,
        u.gender AS userGender,
        u.age AS userAge,
        u.signature AS userSignature,

        <include refid="post_info_state_select"/>,
        <include refid="post_info_select"/>
        FROM urb_post AS p
        LEFT JOIN urb_post_carpool cpool ON p.id = cpool.post_id
        LEFT JOIN urb_job_seeking jobSeeking ON p.id = jobSeeking.post_id
        LEFT JOIN urb_job_offer jobOffer ON p.id = jobOffer.post_id

        <include refid="post_info_join"/>
        WHERE p.is_deleted = 0

        <if test="model.content != null">
            AND p.content like CONCAT('%', #{model.content}, '%')
        </if>
                <if test="model.publishStatus != null">
                    AND p.publish_status = #{model.publishStatus}
                </if>
        <if test="model.auditStatus != null">
            AND p.audit_status = #{model.auditStatus}
        </if>
        <if test="model.publishStatus != null">
            AND p.publish_status = #{model.publishStatus}
        </if>
        <if test="model.categoryId != null">
            AND c.id = #{model.categoryId}
        </if>
        <!-- 距离范围过滤 - 在P2模式和P3下应用 -->
        <if test="model.sortMode == 'distance' and model.latitude != null and model.longitude != null and model.scope != null">
            AND (
            6371 * acos(
            cos(radians(#{model.latitude}))
            * cos(radians(p.latitude))
            * cos(radians(p.longitude) - radians(#{model.longitude}))
            + sin(radians(#{model.latitude})) * sin(radians(p.latitude))
            )
            ) &lt;= #{model.scope}
        </if>

        <if test="model.liked">
            AND EXISTS (
            SELECT 1
            FROM urb_like
            WHERE relevancy_id = p.id
            and type = '0'
            AND user_id = #{model.visitUser}
            AND is_deleted = 0
            )
        </if>
        <if test="model.favorited">
            AND EXISTS (
            SELECT 1
            FROM urb_favorite
            WHERE relevancy_id = p.id
            and type = '0'
            AND user_id = #{model.visitUser}
            AND is_deleted = 0
            )
        </if>
        <if test="model.viewed">
            AND EXISTS (
            SELECT 1
            FROM urb_view_log
            WHERE relevancy_id = p.id
            and type = '0'
            AND user_id = #{model.visitUser}
            AND is_deleted = 0
            )
        </if>
                <if test="model.businessType != null">
                    AND p.business_type = #{model.businessType}
                </if>
        ORDER BY
        CASE WHEN top = '1' THEN '0' ELSE '1' END
        <if test="model.sortMode == 'distance' and model.latitude != null and model.longitude != null">
            ,(6371 * acos(
            cos(radians(#{model.latitude}))
            * cos(radians(p.latitude))
            * cos(radians(p.longitude) - radians(#{model.longitude}))
            + sin(radians(#{model.latitude})) * sin(radians(p.latitude))
            )) ASC
        </if>
        <if test="model.sortMode == 'time' or model.sortMode == null or (model.sortMode == 'distance' and (model.latitude == null or model.longitude == null))">
            , p.publish_time DESC
        </if>
    </select>

    <select id="selectViewHistoryList" resultType="org.springblade.business.post.vo.SupPostVO">
        SELECT
        p.*,
        vh.id as view_id,
        vh.view_time as view_time,
        <include refid="post_info_state_select"/>,
        <include refid="post_info_select"/>
        FROM urb_view_log vh
        LEFT JOIN urb_post p ON vh.relevancy_id = p.id
        where vh.is_deleted = 0
        <include refid="post_info_join"/>
        WHERE p.is_deleted = 0
        <if test="model.status != null">
            AND p.status = #{model.status}
        </if>
        <if test="model.visitUser != null and model.viewed">
            AND vh.user_id = #{model.visitUser}
        </if>
        <if test="model.auditStatus != null">
            AND p.audit_status = #{model.auditStatus}
        </if>
        <if test="model.categoryId != null">
            AND c.id = #{model.categoryId}
        </if>
        <if test="model.viewed">
            AND EXISTS (
            SELECT 1
            FROM urb_view_log
            WHERE relevancy_id = p.id
            AND user_id = #{model.visitUser}
            AND is_deleted = 0
            )
        </if>
        ORDER BY vh.view_time DESC
    </select>
    <select id="countView" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM urb_view_log uvl left join urb_post up
        on uvl.relevancy_id = up.id
        <where>
            <if test="location != null">
                AND up.location like CONCAT('%', #{location}, '%')
            </if>
            and uvl.is_deleted = 0
            and uvl.type = '0'
        </where>
    </select>
    <select id="countShare" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM urb_share_log usl left join urb_post up
        on usl.post_id = up.id
        <where>
            <if test="location != null">
                AND up.location like CONCAT('%', #{location}, '%')
            </if>
        and usl.is_deleted = 0
        </where>
    </select>


    <!--    /**-->
    <!--    * 根据分类id条件分页查询帖子,并且置顶帖子第一个,其它的默认按创建时间排序-->
    <!--    */-->
    <select id="getByCategoryId" resultMap="postResultMap">
        SELECT
        p.*,
        u.nickname,
        u.avatar,
        c.name AS category_name,
        -- 统计字段
        COALESCE(vh.view_count, 0) AS view_count,
        COALESCE(lh.like_count, 0) AS like_count,
        COALESCE(fd.feedback_count, 0) AS feedback_count,
        COALESCE(fh.favorite_count, 0) AS favorite_count
        FROM urb_post p
        LEFT JOIN urb_user u ON p.create_user = u.id
        LEFT JOIN urb_category c ON p.category_id = c.id
        -- 浏览数
        LEFT JOIN (
        SELECT relevancy_id, COUNT(1) AS view_count
        FROM urb_view_log
        WHERE is_deleted = 0
        AND type = '0'
        GROUP BY relevancy_id
        ) vh ON p.id = vh.relevancy_id
        -- 点赞数
        LEFT JOIN (
        SELECT relevancy_id, COUNT(1) AS like_count
        FROM urb_like
        WHERE is_deleted = 0
        AND type = '0'
        GROUP BY relevancy_id
        ) lh ON p.id = lh.relevancy_id
        -- 反馈数
        LEFT JOIN (
        SELECT post_id, COUNT(1) AS feedback_count
        FROM urb_feedback
        GROUP BY post_id
        ) fd ON p.id = fd.post_id
        -- 收藏数
        LEFT JOIN (
        SELECT relevancy_id, COUNT(1) AS favorite_count
        FROM urb_favorite
        where is_deleted = 0
        AND type = '0'
        GROUP BY relevancy_id
        ) fh ON p.id = fh.relevancy_id
        <where>
            <if test="postCategoryId != null">
                p.category_id = #{postCategoryId}
                AND p.is_deleted = 0
            </if>
            <if test="postCategoryId == null">
                p.is_deleted = 0
            </if>
        </where>
        ORDER BY
        CASE WHEN p.top = '1' THEN '0' ELSE '1' END,
        p.create_time DESC
    </select>

    <select id="getUserContactAccessCount" resultType="java.lang.Integer">
        select count(*) from urb_contact_access where user_id = #{model.userId}
        <if test="model.today != null">
            AND create_time &gt;= #{model.today}
        </if>
        <if test="model.postId != null">
            AND post_id=#{model.postId}
        </if>
    </select>
    <select id="selectPostsByInstitutionId" resultMap="postResultMap">
        SELECT
        p.*,
        p.business_type as businessType,
        <if test="model.latitude != null and model.longitude != null">
            (
            6371 * acos(
            cos(radians(#{model.latitude}))
            * cos(radians(p.latitude))
            * cos(radians(p.longitude) - radians(#{model.longitude}))
            + sin(radians(#{model.latitude})) * sin(radians(p.latitude))
            )
            ) AS distance,
        </if>
        -- 顺风车字段
        cpool.id AS carpool_id,
        cpool.region as carpool_region,
        cpool.carpool_type as carpool_carpool_type,
        cpool.departure as carpool_departure,
        cpool.destination as carpool_destination,
        cpool.via as carpool_via,
        cpool.departure_time as carpool_departure_time,
        cpool.empty_seats as carpool_empty_seats,
        cpool.tonnage as carpool_tonnage,
        cpool.post_id AS carpool_post_id,

        -- 招人字段
        jobOffer.id AS job_offer_id,
        jobOffer.post_id AS job_offer_post_id,
        jobOffer.recruit_type AS job_offer_recruit_type,
        jobOffer.workplace AS job_offer_workplace,
        jobOffer.job_title AS job_offer_job_title,
        jobOffer.headcount AS job_offer_headcount,
        jobOffer.salary AS job_offer_salary,
        jobOffer.job_keywords AS job_offer_job_keywords,
        jobOffer.job_description AS job_offer_job_description,

        -- 求职字段
        jobSeeking.id AS job_seeking_id,
        jobSeeking.post_id AS job_seeking_post_id,
        jobSeeking.user_id AS job_seeking_user_id,
        jobSeeking.seeking_type AS job_seeking_type,
        jobSeeking.expected_position AS job_seeking_expected_position,
        jobSeeking.salary_expectation AS job_seeking_salary_expectation,
        jobSeeking.position_preference AS job_seeking_position_preference,

        -- 用户信息
        u.nickname AS userNickname,
        u.mobile AS userMobile,
        u.avatar AS userAvatar,
        u.email AS userEmail,
        u.gender AS userGender,
        u.age AS userAge,
        u.signature AS userSignature,

        <include refid="post_info_state_select"/>,
        <include refid="post_info_select"/>
        FROM urb_post AS p
        LEFT JOIN urb_post_carpool cpool ON p.id = cpool.post_id
        LEFT JOIN urb_job_seeking jobSeeking ON p.id = jobSeeking.post_id
        LEFT JOIN urb_job_offer jobOffer ON p.id = jobOffer.post_id

        <include refid="post_info_join"/>
        WHERE p.is_deleted = 0
        AND p.institution_id = #{model.institutionId}
        ORDER BY
        CASE WHEN p.top = '1' THEN '0' ELSE '1' END,
        p.create_time DESC
    </select>
    <select id="getPostCountByInstitutionId" resultType="java.lang.Integer">
            SELECT COUNT(*) FROM urb_post AS p
            WHERE p.is_deleted = 0
            AND p.institution_id = #{id}
    </select>

    <!-- 批量查询机构帖子数量 -->
    <select id="getPostCountsByInstitutionIds" resultType="java.util.Map">
        SELECT
            p.institution_id as institutionId,
            COUNT(*) as postCount
        FROM urb_post p
        WHERE p.is_deleted = 0
        AND p.institution_id IN
        <foreach collection="institutionIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY p.institution_id
    </select>
    <select id="getPostCountByUserIdAndDate" resultType="java.lang.Long">
        SELECT COUNT(*) FROM urb_post AS p
        WHERE p.is_deleted = 0
        AND p.create_user = #{userId}
        AND DATE_FORMAT(p.create_time, '%Y-%m-%d') &gt;= #{start}
        AND DATE_FORMAT(p.create_time, '%Y-%m-%d') &lt;= #{end}
    </select>

    <!--
    -->
    <!-- 添加点赞记录 -->
    <insert id="insertLike">
        INSERT INTO urb_like (post_id, user_id, like_time)
        VALUES (#{model.postId}, #{model.createUser}, NOW())

    </insert>

    <!-- 取消点赞 -->
    <update id="deleteLike">
        UPDATE
        urb_like
        SET is_deleted = 1
        WHERE relevancy_id = #{model.postId}
          AND user_id = #{model.createUser}
          AND type = '0'
    </update>

    <!-- 添加收藏记录 -->
    <insert id="insertFavorite">
        INSERT INTO urb_favorite (relevancy_id, user_id, fav_time)
        VALUES (#{model.postId}, #{model.createUser}, NOW())
    </insert>
    <insert id="insertShareHistory">
        INSERT INTO urb_share_log (post_id, user_id, share_time)
        VALUES (#{model.postId}, #{model.createUser}, NOW())

    </insert>
    <insert id="addAuditRecord">
        INSERT INTO urb_audit_post (post_id, audit_user, audit_time, audit_status, audit_remark)
        VALUES (#{model.postId}, #{model.auditUser}, #{model.auditTime}, #{model.auditStatus}, #{model.auditRemark})
    </insert>
    <insert id="insertContactAccess">
        INSERT INTO urb_contact_access (post_id, user_id, create_time)
        VALUES (#{model.postId}, #{model.userId}, NOW())
    </insert>
    <insert id="insertViewHistory">
        INSERT INTO urb_view_log (relevancy_id, user_id, view_time,ip)
        VALUES (#{model.postId}, #{model.userId}, NOW(),#{model.ip})
    </insert>

    <!-- 取消收藏 -->
    <update id="deleteFavorite">
        UPDATE
            urb_favorite
        SET is_deleted = 1
        WHERE relevancy_id = #{model.postId}
          AND user_id = #{model.createUser}
          AND type = '0'
    </update>
    <!-- 添加清空收藏的SQL语句 -->
    <delete id="clearFavorites">
        DELETE
        FROM urb_favorite
        WHERE user_id = #{model.createUser}
        AND type = '0'
    </delete>

    <!-- 删除单条浏览记录 -->
    <update id="deleteViewHistory">
        UPDATE
        urb_view_log
        SET is_deleted = 1
        WHERE id = #{id}
          AND user_id = #{userId}
    </update>

    <!-- 获取用户收藏的标签列表 -->
    <select id="selectFavoriteTags" resultType="java.lang.String">
        SELECT c.name
        FROM urb_favorite f
        INNER JOIN urb_post p ON f.relevancy_id = p.id
        INNER JOIN urb_category c ON p.category_id = c.id
        WHERE f.user_id = #{userId}
          AND f.type = '0'
          AND f.is_deleted = 0
          AND p.is_deleted = 0
        GROUP BY c.id, c.name
        ORDER BY COUNT(*) DESC, c.name
        LIMIT 10
    </select>
    <select id="getPostCountByCategoryId" resultType="java.lang.Integer">
            SELECT COUNT(*) FROM urb_post AS p
            WHERE p.is_deleted = 0
            AND p.category_id = #{postCategoryId}
            AND p.status = '1'
            AND p.publish_status = '1'
            AND p.audit_status = 1
    </select>

</mapper>
