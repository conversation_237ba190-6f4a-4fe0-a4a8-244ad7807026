package org.springblade.common.aspect;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springblade.business.user.service.RedisRequestStatsService;

import org.springblade.core.secure.utils.AuthUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class RecordRequestAspect {

	@Autowired
	private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private RedisRequestStatsService redisRequestStatsService;

	// 匹配 org.springblade.business 包下所有 controller 的方法
	@Pointcut("execution(* org.springblade.miniapp..controller.*.*(..))")
	public void requestStatsPointCut() {}

	@Around("requestStatsPointCut()")
    public Object around(ProceedingJoinPoint pjp) throws Throwable {
		// 前置逻辑
		Object result = pjp.proceed(); // 执行目标方法
		//后置逻辑
		Long userId = AuthUtil.getUserId();
		if(userId != null){
			redisRequestStatsService.recordRequest(userId.toString());
		}
		return result;
    }
}
