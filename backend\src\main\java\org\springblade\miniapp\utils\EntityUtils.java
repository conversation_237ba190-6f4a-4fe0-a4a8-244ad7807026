package org.springblade.miniapp.utils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springblade.core.mp.support.Condition;
import org.springblade.modules.system.entity.DataScope;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

public class EntityUtils {

    /**
     * 创建子类实例，并设置 id 和 createUser
     *
     * @param clazz      目标子类的 Class 类型
     * @param id         要设置的 id
     * @param createUser 要设置的创建用户
     * @param <T>        目标子类的类型
     * @return 生成并设置好的子类实例
     */
    public static <T> T createEntity(Class<T> clazz, Long id, Long createUser) {
        try {
            // 1. 通过反射创建实例
            T instance = clazz.getDeclaredConstructor().newInstance();

            // 2. 设置 id（调用 setId 方法）
            Method setIdMethod = clazz.getMethod("setId", Long.class);
            setIdMethod.invoke(instance, id);

            // 3. 设置 createUser（调用 setCreateUser 方法）
            Method setCreateUserMethod = clazz.getMethod("setCreateUser", Long.class);
            setCreateUserMethod.invoke(instance, createUser);

            return instance;
        } catch (NoSuchMethodException | IllegalAccessException | InstantiationException | InvocationTargetException e) {
            throw new RuntimeException("创建实体失败: " + e.getMessage(), e);
        }
    }

	// 获取数据权限用户安全操作wrapper
	public static <T> QueryWrapper<T> getDataScopeWrapper(Class<T> clazz, Long id, Long createUser) {
		return  Condition.getQueryWrapper(createEntity(clazz,id,createUser));
	}
}
