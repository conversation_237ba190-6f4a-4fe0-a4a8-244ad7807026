<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.user.mapper.BusinessCardMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="businessCardResultMap" type="org.springblade.business.user.vo.BusinessCardVO">
        <result column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="company" property="company"/>
        <result column="job_title" property="jobTitle"/>
        <result column="business_profile" property="businessProfile"/>
        <result column="full_name" property="fullName"/>
        <result column="gender" property="gender"/>
        <result column="phone" property="phone"/>
        <result column="address" property="address"/>
        <result column="email" property="email"/>
        <result column="website" property="website"/>
        <result column="weixin" property="weixin"/>
        <result column="avatar" property="avatar"/>
        <result column="images" property="images"/>
        <result column="video" property="video"/>
        <result column="description" property="description"/>
        <result column="is_public" property="isPublic"/>
        <result column="nickname" property="app_nickname"/>
        <result column="mobile" property="app_mobile"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="view_count" property="viewCount"/>
        <result column="distance" property="distance"/>
        <result column="publish_time" property="publishTime"/>

        <!-- 名片状态统计信息 -->
        <association property="stats" javaType="org.springblade.business.user.dto.cardStatsDTO">
            <result column="is_liked" property="isLiked"/>
            <result column="is_favorite" property="isFavorite"/>
            <result column="view_count" property="viewCount"/>
            <result column="like_count" property="likeCount"/>
            <result column="favorite_count" property="favoriteCount"/>
        </association>
    </resultMap>

    <sql id="card_info_join">
        LEFT JOIN urb_user uu ON ubs.create_user = uu.id
        LEFT JOIN (
            SELECT relevancy_id, COUNT(1) as view_count
            FROM urb_view_log
            where type = '2'
            and is_deleted = 0
            GROUP BY relevancy_id
        ) v ON ubs.id = v.relevancy_id
        LEFT JOIN (
            SELECT relevancy_id, COUNT(1) as like_count
            FROM urb_like
            where is_deleted = 0
            AND type = '2'
            GROUP BY relevancy_id
        ) l ON ubs.id = l.relevancy_id
        LEFT JOIN (
            SELECT relevancy_id, COUNT(1) as favorite_count
            FROM urb_favorite
            where is_deleted = 0
            AND type = '2'
            GROUP BY relevancy_id
        ) f ON ubs.id = f.relevancy_id
        <!-- 当前用户的点赞状态 -->
        <if test="params.visitUser != null">
        LEFT JOIN (
            SELECT relevancy_id, 1 as is_liked
            FROM urb_like
            WHERE user_id = #{params.visitUser}
            AND type = '2'
            AND is_deleted = 0
        ) ul ON ubs.id = ul.relevancy_id
        <!-- 当前用户的收藏状态 -->
        LEFT JOIN (
            SELECT relevancy_id, 1 as is_favorite
            FROM urb_favorite
            WHERE user_id = #{params.visitUser}
            AND type = '2'
            AND is_deleted = 0
        ) uf ON ubs.id = uf.relevancy_id
        </if>
    </sql>
    <select id="selectBusinessCardPage" resultMap="businessCardResultMap">
        select ubs.*, uu.nickname, uu.mobile,
        COALESCE(v.view_count, 0) as view_count,
        COALESCE(l.like_count, 0) as like_count,
        COALESCE(f.favorite_count, 0) as favorite_count
        from urb_business_card ubs
        <include refid="card_info_join"/>
                 where ubs.is_deleted = 0
                 <if test="businessCard.fullName!=null">
                     and ubs.full_name like concat('%',#{businessCard.fullName},'%')
                 </if>
                  <if test="businessCard.jobTitle!=null">
                      and ubs.job_title like concat('%',#{businessCard.jobTitle},'%')
                  </if>
                 <if test="businessCard.id!=null">
                     and ubs.id = #{businessCard.id}
                 </if>
                   <if test="businessCard.isPublic!=null">
                    and ubs.is_public = #{businessCard.isPublic}
                  </if>
                   <if test="businessCard.publishStatus!=null">
                    and ubs.publish_status = #{businessCard.publishStatus}
                  </if>
        <if test="businessCard.cardType!=null">
            and ubs.card_type = #{params.cardType}
        </if>
        order by ubs.publish_time desc
    </select>
    <select id="selectCardList" resultMap="businessCardResultMap">
        select ubs.*, uu.nickname, uu.mobile,
        COALESCE(v.view_count, 0) as view_count,
        COALESCE(l.like_count, 0) as like_count,
        COALESCE(f.favorite_count, 0) as favorite_count,
        <if test="params.visitUser != null">
        COALESCE(ul.is_liked, 0) as is_liked,
        COALESCE(uf.is_favorite, 0) as is_favorite
        </if>
        <if test="params.visitUser == null">
        0 as is_liked,
        0 as is_favorite
        </if>
        <if test="params.latitude != null and params.longitude != null">
            ,(
            6371 * acos(
            cos(radians(#{params.latitude}))
            * cos(radians(ubs.latitude))
            * cos(radians(ubs.longitude) - radians(#{params.longitude}))
            + sin(radians(#{params.latitude})) * sin(radians(ubs.latitude))
            )
            ) AS distance
        </if>
        from urb_business_card ubs
        <include refid="card_info_join"/>
        where ubs.is_deleted = 0
          and ubs.publish_status = 1
          and ubs.is_public = 1
        <if test="params.auditStatus!=null">
            and ubs.audit_status = #{params.auditStatus}
        </if>
        <if test="params.publishStatus!=null">
            and ubs.publish_status = #{params.publishStatus}
        </if>
        <if test="params.fullName!=null">
            and ubs.full_name like concat('%',#{params.fullName},'%')
        </if>
        <if test="params.jobTitle!=null">
            and ubs.job_title like concat('%',#{params.jobTitle},'%')
        </if>
        <if test="params.cardType!=null">
            and ubs.card_type = #{params.cardType}
        </if>
        <!-- 距离范围过滤 -->
        <if test="params.latitude != null and params.longitude != null and params.scope != null">
            AND (
            6371 * acos(
            cos(radians(#{params.latitude}))
            * cos(radians(ubs.latitude))
            * cos(radians(ubs.longitude) - radians(#{params.longitude}))
            + sin(radians(#{params.latitude})) * sin(radians(ubs.latitude))
            )
            ) &lt;= #{params.scope}
        </if>
        <if test="params.latitude != null and params.longitude != null">
        order by distance asc
        </if>
        order by ubs.publish_time desc
    </select>
</mapper>
