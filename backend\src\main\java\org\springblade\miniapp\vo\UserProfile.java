package org.springblade.miniapp.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springblade.business.user.entity.Contact;

@Data
@Accessors(chain = true)
public class UserProfile extends Contact{
	/**
	 * 昵称
	 */
	@Schema(description = "昵称")
	private String nickname;
	/**
	 * 手机号
	 */
	@Schema(description = "手机号")
	private String mobile;
	/**
	 * 性别
	 */
	@Schema(description = "性别")
	private String gender;
	/**
	 * 个性签名
	 */
	@Schema(description = "个性签名")
	private String signature;

	@Schema(description = "头像")
	private String avatar;

	@Schema(description = "生日")
	private String birthday;
	@Schema(description = "地区")
	private String region;
	@Schema(description = "邮箱")
	private String email;

	/**
	 * 动态总数
	 */
	@Schema(description = "帖子总数")
	private Long postCount;

	/**
	 * 反馈总数
	 */
	@Schema(description = "反馈总数")
	private Long feedbackCount;



}
