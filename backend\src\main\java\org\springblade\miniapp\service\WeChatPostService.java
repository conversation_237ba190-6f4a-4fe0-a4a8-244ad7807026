package org.springblade.miniapp.service;

import org.springblade.business.post.entity.SupPost;
import org.springblade.business.post.entity.Tag;
import org.springblade.business.post.request.PostCreateRequest;
import org.springblade.business.post.vo.SupPostVO;
import org.springblade.core.mp.support.Query;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.miniapp.command.TagCreateCommand;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface WeChatPostService {
	/**
	 * 发布广告
	 */
	Boolean submitPost(SupPostVO post);

	/**
	 * 保存草稿
	 */
	Long saveDraft(SupPostVO post);

	/**
	 * 获取草稿列表
	 */
	IPage<SupPostVO> getDraftList(Query query);

	/**
	 * 获取草稿详情
	 */
	SupPostVO getDraftDetail(Long id);

	/**
	 * 删除草稿
	 */
	Boolean deleteDraft(Long id);

	/**
	 * 删除帖子
	 */
	Boolean deletePost(Long id);

	/**
	 * 切换帖子完成状态
	 */
	Boolean toggleCompleted(Long id);

	/**
	 * 获取我的帖子列表
	 */
	IPage<SupPostVO> getMyPosts(Query query);

	/**
	 * 获取我点赞的帖子列表
	 */
	IPage<SupPostVO> getLikedPosts(Query query);

	/**
	 * 获取我收藏的帖子列表
	 */
	IPage<SupPostVO> getFavoritePosts(Query query);

	/**
	 * 获取收藏标签列表
	 */
	List<String> getFavoriteTags();

	/**
	 * 获取浏览记录
	 */
	IPage<SupPostVO> getViewHistory(Query query);

	/**
	 * 清空浏览记录
	 */
	boolean clearViewHistory();

	/**
	 * 删除单条浏览记录
	 */
	Boolean deleteViewHistory(Long id);


	boolean addViewHistory(Long postId);

	IPage<SupPostVO> getCallHistory(Query query);

	Boolean clearCallHistory();

	Boolean addCallHistory(Long postId);

	List<Tag> getTagsByCategory(Long id);

	@Transactional(rollbackFor = Exception.class)
	Boolean createCustomTag(TagCreateCommand request);

	IPage<SupPostVO> getPostListByCategoryId(Long postCategoryId, Query query);

	/**
	 * 点赞/取消点赞帖子
	 */
	Boolean toggleLike(Long postId);

	/**
	 * 收藏/取消收藏帖子
	 */
	Boolean toggleFavorite(Long postId);

	/**
	 * 清空收藏
	 */
	Boolean clearFavorites();

	/**
	 * 获取帖子详情
	 */
	SupPostVO getPostDetail(Long id);


	IPage<SupPostVO> getHomePostList(Query query, SupPostVO postVO);

	SupPost createPost(PostCreateRequest createRequest);

	Long countPost(String location);

	Long countView(String location);

	Long countShare(String location);

	boolean addShareHistory(Long postId);

	/**
	 * 记录分享行为
	 * @param postId 帖子ID
	 * @param type 分享类型
	 * @return 是否成功
	 */
	Boolean recordShare(Long postId, String type);

	String  getContact(Long id);

    Boolean moveToDraft(Long id);

	Boolean publishFromDraft(Long id);
}
