package org.springblade.miniapp.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;

import io.swagger.v3.oas.annotations.Parameter;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springblade.business.cooperation.entity.CooperationLeads;
import org.springblade.business.cooperation.service.ICooperationLeadsService;
import org.springblade.business.group.entity.GroupInfo;
import org.springblade.business.group.service.IGroupCategoryService;
import org.springblade.business.group.service.IGroupInfoService;
import org.springblade.business.group.vo.GroupCategoryVO;
import org.springblade.business.group.vo.GroupInfoVO;
import org.springblade.business.group.wrapper.GroupInfoWrapper;
import org.springblade.business.post.service.ISupPostService;
import org.springblade.business.post.vo.FeedbackVO;
import org.springblade.business.post.vo.SupPostVO;
import org.springblade.business.user.dto.WeUserDTO;
import org.springblade.business.user.entity.BusinessCard;
import org.springblade.business.user.service.IBusinessCardService;
import org.springblade.business.user.service.IWeUserService;
import org.springblade.business.user.vo.BusinessCardVO;
import org.springblade.business.user.vo.WeUserVO;
import org.springblade.business.user.wrapper.BusinessCardWrapper;
import org.springblade.common.anno.AccessLimit;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.miniapp.service.WeChatPostService;
import org.springblade.miniapp.service.WeChatUserService;
import org.springblade.miniapp.vo.UserProfile;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@AllArgsConstructor
@RequestMapping("/blade-chat/user")
@io.swagger.v3.oas.annotations.tags.Tag(name = "微信用户信息", description = "用户信息接口")
public class WeChatUserController {

	@Resource
	private WeChatUserService weChatUserService;


	@Resource
	private final IGroupInfoService groupInfoService;

	@Resource
	private final IGroupCategoryService groupCategoryService;

	@Resource
	private final ICooperationLeadsService cooperationLeadsService;



	// 通过ID查询用户个人信息
	@Operation(summary = "详情", description = "通过ID查询用户个人信息")
	@ApiOperationSupport(order = 1)
	@GetMapping("/info")
	@AccessLimit(seconds = 30)
	public R<UserProfile> info() {
		return R.data(weChatUserService.getUserInfo(AuthUtil.getUserId()));
	}

	/**
	 * 修改用户信息
	 */
	@PostMapping("/update")
	public R update(@RequestBody UserProfile userProfile) {
		return R.status(weChatUserService.updateUserInfo(userProfile));
	}

	/**
	 * 获取用户统计数据
	 */
	@GetMapping("/stats")
	@AccessLimit(seconds = 30)
	@Operation(summary = "获取用户统计数据", description = "获取用户的帖子数、点赞数、收藏数")
	public R<Map<String, Object>> getUserStats() {
		return R.data(weChatUserService.getUserStats());
	}

	/**
	 * 查询当前用户的所有帖子
	 */
	@AccessLimit(seconds = 30)
	@GetMapping("/post/personal-list")
	@Operation(summary = "获取帖子列表")
	public R<IPage<SupPostVO>> getPostListByUserId(Query  query) {
//		Long testId =1899127984496861186L;
		return R.data(weChatUserService.getPostListByUserId(AuthUtil.getUserId(), query));
//		return R.data(weChatUserService.getPostListByUserId(testId, query));
	}

	/**
	 * 获取用户的所有反馈
	 */
	@AccessLimit(seconds = 30)
	@GetMapping("/feedback/list")
	@Operation(summary = "获取用户反馈列表")
	public R<IPage<FeedbackVO>> getFeedbackList(Query query) {
//		Long testId =1899127984496861186L;
		return R.data(weChatUserService.getFeedbackList(AuthUtil.getUserId(), query));
//		return R.data(weChatUserService.getFeedbackList(testId, query));
	}



	/**
	 * 自定义分页 群信息表
	 */
	@GetMapping("/group/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入groupInfo")
	public R<IPage<GroupInfoVO>> page(GroupInfoVO groupInfo, Query query) {
		IPage<GroupInfoVO> pages = groupInfoService.selectGroupInfoPage(Condition.getPage(query), groupInfo);
		return R.data(pages);
	}

	/**
	 * 详情-微信链接
	 */
	@GetMapping("/group/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入groupInfo")
	public R<GroupInfoVO> detail(GroupInfo groupInfo) {
		GroupInfo detail = groupInfoService.getOne(Condition.getQueryWrapper(groupInfo));
		return R.data(GroupInfoWrapper.build().entityVO(detail));
	}

	/**
	 * 自定义分页 群分类表
	 */
	@GetMapping("/group/category/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入groupCategory")
	public R<IPage<GroupCategoryVO>> page(GroupCategoryVO groupCategory, Query query) {
		IPage<GroupCategoryVO> pages = groupCategoryService.selectGroupCategoryPage(Condition.getPage(query), groupCategory);
		return R.data(pages);
	}


	/**
	 * 获取用户积分
	 */
	@GetMapping("/points")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "获取用户积分", description = "获取用户积分")
	public R<Integer> getPoints() {
		return R.data(weChatUserService.getPoints(AuthUtil.getUserId()));
	}


	/**
	 * 新增 合作线索表
	 */
	@PostMapping("/cooperation/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入cooperationLeads")
	public R save(@Valid @RequestBody CooperationLeads cooperationLeads) {
		return R.status(cooperationLeadsService.save(cooperationLeads));
	}





}
