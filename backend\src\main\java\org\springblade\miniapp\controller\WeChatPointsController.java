/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.miniapp.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import org.springblade.business.points.entity.Coupon;
import org.springblade.business.points.service.ICouponService;
import org.springblade.business.points.vo.CouponVO;
import org.springblade.business.points.wrapper.CouponWrapper;
import org.springblade.business.points.entity.PointsExchange;
import org.springblade.business.points.service.IPointsExchangeService;
import org.springblade.business.points.vo.PointsExchangeVO;
import org.springblade.business.points.entity.PointsGoods;
import org.springblade.business.points.service.IPointsGoodsService;
import org.springblade.business.points.vo.PointsGoodsVO;
import org.springblade.business.points.wrapper.PointsGoodsWrapper;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.miniapp.service.WeChatPointsService;
import org.springframework.web.bind.annotation.*;

/**
 * 积分商城商品表 控制器
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-chat/points")
@io.swagger.v3.oas.annotations.tags.Tag(name = "积分接口", description = "积分接口")
public class WeChatPointsController extends BladeController {

	private IPointsGoodsService pointsGoodsService;
	private IPointsExchangeService pointsExchangeService;
	private ICouponService couponService;
	private WeChatPointsService pointsService;

	/**
	 * 查看积分商品详情
	 */
	@GetMapping("/pointsGoods/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "积分商品详情", description = "传入pointsGoods")
	public R<PointsGoodsVO> detail(PointsGoods pointsGoods) {
		PointsGoods detail = pointsGoodsService.getOne(Condition.getQueryWrapper(pointsGoods));
		return R.data(PointsGoodsWrapper.build().entityVO(detail));
	}



	/**
	 * 自定义分页 积分商城商品
	 */
	@GetMapping("/pointsGoods/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "积分商城商品分页", description = "传入pointsGoods")
	public R<IPage<PointsGoodsVO>> page(PointsGoodsVO pointsGoods, Query query) {
		IPage<PointsGoodsVO> pages = pointsGoodsService.selectPointsGoodsPage(Condition.getPage(query), pointsGoods);
		return R.data(pages);
	}

	/**
	 * 自定义分页 当前用户积分兑换记录表
	 */
	@GetMapping("/pointsExchange/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "当前用户积分兑换记录分页", description = "传入pointsExchange")
	public R<IPage<PointsExchangeVO>> page(PointsExchangeVO pointsExchange, Query query) {
		IPage<PointsExchangeVO> pages = pointsExchangeService.selectPointsExchangePage(Condition.getPage(query), pointsExchange);
		return R.data(pages);
	}

	/**
	 * 积分兑换记录表详情  关联积分商品表查询积分商品信息返回
	 */
	@GetMapping("/pointsExchange/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "积分兑换记录表详情", description = "传入pointsExchange 的id就行")
	public R<PointsExchange> detail(PointsExchange pointsExchange) {
		PointsExchange detail = pointsExchangeService.getPointsPointsExchangeDetail(pointsExchange);
		return R.data(detail);
	}

	/**
	 *查看用户的优惠卷   优惠卷关联商品表查询商品信息返回
	 */
	@GetMapping("/coupon/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "查看用户的优惠卷分页", description = "传入coupon")
	public R<IPage<CouponVO>> list(Coupon coupon, Query query) {
		IPage<Coupon> pages = couponService.pageCoupon(coupon, query);
		return R.data(CouponWrapper.build().pageVO(pages));
	}


	/**
	 * 用积分兑换积分商品生成又优惠卷，并且保存记录
	 */
	@PostMapping("/exchange")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "用积分兑换积分商品", description = "传入coupon")
	public R<Boolean> exchange(@RequestBody Coupon coupon) {
		return R.status(couponService.exchange(coupon));
	}





}
