package org.springblade.miniapp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.business.post.dto.MessageCreateDTO;
import org.springblade.business.post.dto.MessageQueryDTO;
import org.springblade.business.post.dto.MessageTemplateCreateDTO;
import org.springblade.business.post.dto.MessageTemplateUpdateDTO;
import org.springblade.common.constant.bizz.MessageTypeEnum;
import org.springblade.miniapp.vo.*;
import java.util.List;

public interface WeChatMessageService {
    IPage<MessageVO> queryMessages(MessageQueryDTO queryDTO);
    void markAsRead(Long id);
    void batchDelete(List<Long> ids);
    MessageVO createMessage(MessageCreateDTO createDTO);
    List<MessageTemplateVO> getAllTemplates();
    void updateTemplate(MessageTemplateUpdateDTO updateDTO);

    /**
     * 新增消息模板
     */
    void addTemplate(MessageTemplateCreateDTO createDTO);
    /**
     * 业务直接调用：创建消息
     */
    void createMessage(Long userId, MessageTypeEnum type, java.util.Map<String, Object> templateParams, Long relatedId);
}
