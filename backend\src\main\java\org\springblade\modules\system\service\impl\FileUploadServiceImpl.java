/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.modules.system.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.modules.system.config.CloudStorageConfig;
import org.springblade.modules.system.entity.FileUpload;
import org.springblade.modules.system.mapper.FileUploadMapper;
import org.springblade.modules.system.service.ICloudStorageService;
import org.springblade.modules.system.service.IFileUploadService;
import org.springblade.modules.system.vo.FileUploadVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 文件上传服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileUploadServiceImpl extends ServiceImpl<FileUploadMapper, FileUpload> implements IFileUploadService {

    private final CloudStorageConfig config;
    private final ICloudStorageService cloudStorageService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FileUpload uploadFile(MultipartFile file, String uploadSource, String businessType, Long businessId) {
        try {
            // 验证文件
            validateFile(file);

            // 生成文件信息
            String originalName = file.getOriginalFilename();
            String fileExtension = FileUtil.extName(originalName);
            String fileName = generateFileName(fileExtension);
            String path = generateFilePath(fileName, uploadSource);

            // 计算文件MD5
            String fileMd5 = DigestUtil.md5Hex(file.getInputStream());

			//对比MD5判断该文件是否已存在
			List<FileUpload> fileUploads = list(new LambdaQueryWrapper<FileUpload>().eq(FileUpload::getFileMd5, fileMd5));
			if (fileUploads != null && !fileUploads.isEmpty()) {
				return fileUploads.get(0);
			}

            // 上传到云存储
            String accessUrl = cloudStorageService.uploadFile(file, path);


            // 生成缩略图（如果是图片）
            String thumbnailUrl = null;
            if (isImage(file.getContentType()) && config.getUpload().getGenerateThumbnail()) {
                String thumbnailPath = generateThumbnailPath(fileName, uploadSource);
                thumbnailUrl = cloudStorageService.generateThumbnail(
                    path,
                    thumbnailPath,
                    config.getUpload().getThumbnailWidth(),
                    config.getUpload().getThumbnailHeight()
                );
            }

            // 保存文件记录
            FileUpload fileUpload = new FileUpload();
            fileUpload.setOriginalName(originalName);
            fileUpload.setFileName(fileName);
            fileUpload.setFileExtension(fileExtension);
            fileUpload.setFileSize(file.getSize());
            fileUpload.setContentType(file.getContentType());
            fileUpload.setFileCategory(getFileCategory(file.getContentType()));
            fileUpload.setUploadSource(uploadSource);
            fileUpload.setStorageProvider(config.getProvider());
            fileUpload.setBucketName(getBucketName());
            fileUpload.setStoragePath(path);
            fileUpload.setAccessUrl(accessUrl);
            fileUpload.setThumbnailUrl(thumbnailUrl);
            fileUpload.setFileMd5(fileMd5);
            fileUpload.setStatus(1); // 上传成功
            fileUpload.setBusinessId(businessId);
            fileUpload.setBusinessType(businessType);
            fileUpload.setCreateUser(AuthUtil.getUserId());

            save(fileUpload);
            return fileUpload;
        } catch (Exception e) {
            log.error("文件上传失败", e);
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<FileUpload> uploadFiles(List<MultipartFile> files, String uploadSource, String businessType, Long businessId) {
        List<FileUpload> results = new ArrayList<>();
        for (MultipartFile file : files) {
            try {
                FileUpload fileUpload = uploadFile(file, uploadSource, businessType, businessId);
                results.add(fileUpload);
            } catch (Exception e) {
                log.error("批量上传文件失败: {}", file.getOriginalFilename(), e);
                // 继续处理其他文件
            }
        }
        return results;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteFile(Long id) {
        FileUpload fileUpload = getById(id);
        if (fileUpload == null) {
            return false;
        }

        try {
            // 删除云存储中的文件
            cloudStorageService.deleteFile(fileUpload.getStoragePath());

            // 删除缩略图
            if (StrUtil.isNotBlank(fileUpload.getThumbnailUrl())) {
                String thumbnailPath = fileUpload.getStoragePath().replace(".", "_thumb.");
                cloudStorageService.deleteFile(thumbnailPath);
            }

            // 删除数据库记录
            return removeById(id);
        } catch (Exception e) {
            log.error("删除文件失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteFiles(List<Long> ids) {
        List<FileUpload> fileUploads = listByIds(ids);
        boolean success = true;

        for (FileUpload fileUpload : fileUploads) {
            if (!deleteFile(fileUpload.getId())) {
                success = false;
            }
        }

        return success;
    }

    @Override
    public List<FileUpload> getFilesByBusiness(String businessType, Long businessId) {
        LambdaQueryWrapper<FileUpload> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileUpload::getBusinessType, businessType)
                   .eq(FileUpload::getBusinessId, businessId)
                   .eq(FileUpload::getStatus, 1)
                   .orderByDesc(FileUpload::getCreateTime);
        return list(queryWrapper);
    }

    @Override
    public IPage<FileUploadVO> selectFileUploadPage(IPage<FileUploadVO> page, FileUploadVO fileUpload) {
        return page.setRecords(baseMapper.selectFileUploadPage(page, fileUpload));
    }

    @Override
    public Object getFileStats() {
        // 统计文件数量、大小等信息
        long totalFiles = count();
        long totalSize = baseMapper.getTotalFileSize();

        // 按文件类型统计
        List<Map<String, Object>> typeStats = baseMapper.getFileTypeStats();

        // 按上传来源统计
        List<Map<String, Object>> sourceStats = baseMapper.getUploadSourceStats();

        return Map.of(
            "totalFiles", totalFiles,
            "totalSize", totalSize,
            "typeStats", typeStats,
            "sourceStats", sourceStats
        );
    }

    private void validateFile(MultipartFile file) {
        if (file.isEmpty()) {
            throw new RuntimeException("文件不能为空");
        }

        // 检查文件大小
        long maxSize = config.getUpload().getMaxFileSize() * 1024 * 1024; // 转换为字节
        if (file.getSize() > maxSize) {
            throw new RuntimeException("文件大小不能超过 " + config.getUpload().getMaxFileSize() + "MB");
        }

        // 检查文件类型
        String contentType = file.getContentType();
        if (!Arrays.asList(config.getUpload().getAllowedTypes()).contains(contentType)) {
            throw new RuntimeException("不支持的文件类型: " + contentType);
        }
    }

    private String generateFileName(String fileExtension) {
        return IdUtil.fastSimpleUUID() + "." + fileExtension;
    }

    private String generateFilePath(String fileName, String uploadSource) {
        String datePath = DateUtil.format(DateUtil.now(), "yyyy/MM/dd");
        return config.getUpload().getPathPrefix() + "/" + uploadSource + "/" + datePath + "/" + fileName;
    }

    private String generateThumbnailPath(String fileName, String uploadSource) {
        String datePath = DateUtil.format(DateUtil.now(), "yyyy/MM/dd");
        String nameWithoutExt = fileName.substring(0, fileName.lastIndexOf("."));
        return config.getUpload().getPathPrefix() + "/" + uploadSource + "/" + datePath + "/" + nameWithoutExt + "_thumb.jpg";
    }

    private boolean isImage(String contentType) {
        return contentType != null && contentType.startsWith("image/");
    }

    private String getFileCategory(String contentType) {
        if (contentType == null) {
            return "unknown";
        }
        if (contentType.startsWith("image/")) {
            return "image";
        } else if (contentType.startsWith("video/")) {
            return "video";
        } else if (contentType.startsWith("audio/")) {
            return "audio";
        } else if (contentType.startsWith("text/")) {
            return "text";
        } else if (contentType.contains("pdf")) {
            return "document";
        } else if (contentType.contains("word") || contentType.contains("excel") || contentType.contains("powerpoint")) {
            return "office";
        } else {
            return "other";
        }
    }

    private String getBucketName() {
        if ("aliyun-oss".equals(config.getProvider())) {
            return config.getAliyunOss().getBucketName();
        } else if ("cloudflare".equals(config.getProvider())) {
            return config.getCloudflare().getBucketName();
        }
        return "";
    }
}
