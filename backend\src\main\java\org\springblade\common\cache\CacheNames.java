/**
 * Copyright (c) 2018-2099, Chill <PERSON><PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.common.cache;

/**
 * 缓存名
 *
 * <AUTHOR>
 */
public interface CacheNames {

	String NOTICE_ONE = "notice:one";

	String DICT_VALUE = "dict:value";
	String DICT_LIST = "dict:list";

	String CAPTCHA_KEY = "blade:auth::captcha:";

	//菜单缓存名
	String WECHAT_MENU_LIST="wechat:menu:list";
	//所有启用的分类
	String WECHAT_CATEGORY_LIST="wechat:category:list";
	//所有分类
	String WECHAT_CATEGORY_ALL="wechat:category:all";
	//分类的所有标签
	String WECHAT_CATEGORY_TAGS="wechat:category:tags";
	//分类的所有表单
	String WECHAT_CATEGORY_FORMS="wechat:category:forms";
	//全局参数缓存名
	String WECHAT_PARAM_ALL = "wechat:param:all";
	//积分商品缓存名
	String WECHAT_POINTS_GOODS_LIST = "wechat:points:goods:list";

	String WECHAT_REGIN_SELECT = "wechat:regin:select";


}
