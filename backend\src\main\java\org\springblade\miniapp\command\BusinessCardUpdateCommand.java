package org.springblade.miniapp.command;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 用户创建卡片
 *
 * <AUTHOR>
 * @version 1.0
 * @email <EMAIL>
 * @website duofan.top
 * @date 2025/8/1
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户创建卡片")
public class BusinessCardUpdateCommand implements Serializable {
	@Schema(description = "id")
	private Long id;

	/**
	 * 公司名称
	 */
	@Schema(description = "公司名称")
	private String company;
	/**
	 * 职位
	 */
	@Schema(description = "职位")
	private String jobTitle;
	/**
	 * 业务简介
	 */
	@Schema(description = "业务简介")
	private String businessProfile;
	/**
	 * 姓名
	 */
	@Schema(description = "姓名")
	private String fullName;
	/**
	 * 性别(0-保密，1-男，2-女)
	 */
	@Schema(description = "性别(0-保密，1-男，2-女)")
	private Integer gender;
	/**
	 * 电话
	 */
	@Schema(description = "电话")
	private String phone;
	/**
	 * 地址
	 */
	@Schema(description = "地址")
	private String address;
	/**
	 * 邮箱
	 */
	@Schema(description = "邮箱")
	private String email;
	/**
	 * 网址
	 */
	@Schema(description = "网址")
	private String website;
	/**
	 * 微信
	 */
	@Schema(description = "微信")
	private String weixin;
	/**
	 * 头像
	 */
	@Schema(description = "头像")
	private String avatar;
	/**
	 * 图片
	 */
	@Schema(description = "图片")
	private String images;
	/**
	 * 视频
	 */
	@Schema(description = "视频")
	private String video;
	/**
	 * 是否公开（0-否，1-是）
	 */
	@Schema(description = "是否公开（0-否，1-是）")
	private Integer isPublic;

	/**
	 * 经度
	 */
	@Schema(description = "经度")
	private BigDecimal longitude;

	/**
	 * 纬度
	 */
	@Schema(description = "纬度")
	private BigDecimal latitude;

}
