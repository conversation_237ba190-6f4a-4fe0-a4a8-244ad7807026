/**
 * Copyright (c) 2018-2099, Chill <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.modules.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springblade.modules.system.entity.FileUpload;
import org.springblade.modules.system.vo.FileUploadVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 文件上传服务接口
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
public interface IFileUploadService extends IService<FileUpload> {

    /**
     * 上传文件
     *
     * @param file 文件
     * @param uploadSource 上传来源
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 文件上传记录
     */
    FileUpload uploadFile(MultipartFile file, String uploadSource, String businessType, Long businessId);

    /**
     * 批量上传文件
     *
     * @param files 文件列表
     * @param uploadSource 上传来源
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 文件上传记录列表
     */
    List<FileUpload> uploadFiles(List<MultipartFile> files, String uploadSource, String businessType, Long businessId);

    /**
     * 删除文件
     *
     * @param id 文件ID
     * @return 是否成功
     */
    boolean deleteFile(Long id);

    /**
     * 批量删除文件
     *
     * @param ids 文件ID列表
     * @return 是否成功
     */
    boolean deleteFiles(List<Long> ids);

    /**
     * 根据业务信息查询文件
     *
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 文件列表
     */
    List<FileUpload> getFilesByBusiness(String businessType, Long businessId);

    /**
     * 分页查询文件
     *
     * @param page 分页参数
     * @param fileUpload 查询条件
     * @return 分页结果
     */
    IPage<FileUploadVO> selectFileUploadPage(IPage<FileUploadVO> page, FileUploadVO fileUpload);

    /**
     * 获取文件统计信息
     *
     * @return 统计信息
     */
    Object getFileStats();
} 