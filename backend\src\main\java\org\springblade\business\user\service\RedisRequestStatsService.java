package org.springblade.business.user.service;

import cn.hutool.core.date.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.data.redis.core.*;
import org.springframework.stereotype.Service;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
public class RedisRequestStatsService implements CommandLineRunner {

    private static final String KEY_ACTIVE_USERS_PREFIX = "user:active:";
    private static final String KEY_LOGIN_COUNT = "user:request:count";
    private static final String KEY_LAST_LOGIN_TIME = "user:request:last_time";
    private static final int ACTIVE_DAYS_TO_KEEP = 7; // 保留7天数据

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 记录用户登录信息
     */
    public void recordRequest(String userId) {
        String todayKey = getTodayActiveUsersKey();

        // 1. 记录今日活跃用户 (Set结构)
		redisTemplate.opsForSet().add(todayKey, userId);
        redisTemplate.expire(todayKey, ACTIVE_DAYS_TO_KEEP, TimeUnit.DAYS);

        // 2. 记录请求次数 (Hash结构)
		redisTemplate.opsForHash().put(KEY_LOGIN_COUNT, userId, String.valueOf(1));
		redisTemplate.expire(KEY_LOGIN_COUNT, 1, TimeUnit.DAYS);

        // 3. 记录最后请求时间 (Hash结构，时间戳)
        long timestamp = System.currentTimeMillis() / 1000;
		redisTemplate.opsForHash().put(KEY_LAST_LOGIN_TIME, userId, String.valueOf((int) timestamp));
    }

    /**
     * 获取今日活跃用户数
     */
    public Long getTodayActiveUserCount() {
        String todayKey = getTodayActiveUsersKey();
        return redisTemplate.opsForSet().size(todayKey);
    }

    /**
     * 获取指定日期的活跃用户数
     */
    public Long getActiveUserCount(String date) {
        String key = KEY_ACTIVE_USERS_PREFIX + date;
        return redisTemplate.opsForSet().size(key);
    }

    /**
     * 获取近N天的活跃用户数 (去重)
     */
    public Long getActiveUserCountInDays(int days) {
        List<String> keys = generateActiveUserKeys(days);
        if (keys.isEmpty()) return 0L;

        // 使用临时key合并结果
        String tempKey = "temp:active:users:" + UUID.randomUUID();
        try {
            // 合并多个Set到临时key
            redisTemplate.opsForSet().unionAndStore(
                keys.get(0),
                keys.subList(1, keys.size()),
                tempKey
            );
            return redisTemplate.opsForSet().size(tempKey);
        } finally {
            // 删除临时key
            redisTemplate.delete(tempKey);
        }
    }

    /**
     * 获取用户的总请求次数
     */
    public String getUserRequestCount(String userId) {
        HashOperations<String, String, String> hashOps = redisTemplate.opsForHash();
        return hashOps.get(KEY_LOGIN_COUNT, userId);
    }


    /**
     * 获取用户的最后请求时间
     */
    public Long getUserLastRequestTime(String userId) {
        HashOperations<String, String, Integer> hashOps = redisTemplate.opsForHash();
        Integer timestamp = hashOps.get(KEY_LAST_LOGIN_TIME, userId);
        return timestamp != null ? timestamp.longValue() : null;
    }

    /**
     * 获取指定日期的活跃用户列表
     */
    public Set<Object> getActiveUserList(String date) {
        String key = KEY_ACTIVE_USERS_PREFIX + date;
        return redisTemplate.opsForSet().members(key);
    }

    /**
     * 生成今日活跃用户Key
     */
    private String getTodayActiveUsersKey() {
        return KEY_ACTIVE_USERS_PREFIX + DateUtil.format(new Date(),"YYYYMMdd");
    }

    /**
     * 生成近N天的活跃用户Key列表
     */
    private List<String> generateActiveUserKeys(int days) {
        List<String> keys = new ArrayList<>();
        LocalDate currentDate = LocalDate.now();
        for (int i = 0; i < days; i++) {
            String date = DateUtil.format(new Date(),"YYYYMMdd");
            keys.add(KEY_ACTIVE_USERS_PREFIX + date);
        }
        return keys;
    }

	@Override
	public void run(String... args) throws Exception {
		this.recordRequest("2");
	}
}
