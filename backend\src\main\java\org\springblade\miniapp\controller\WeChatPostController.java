package org.springblade.miniapp.controller;

import org.springblade.business.config.service.IFormConfigService;
import org.springblade.business.config.vo.FormConfigVO;
import org.springblade.business.post.entity.SupPost;
import org.springblade.business.post.entity.Tag;
import org.springblade.business.post.request.PostCreateRequest;
import org.springblade.business.post.vo.SupPostVO;
import org.springblade.common.anno.AccessLimit;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.miniapp.command.TagCreateCommand;
import org.springblade.miniapp.service.WeChatPostService;
import org.springframework.web.bind.annotation.*;

import com.baomidou.mybatisplus.core.metadata.IPage;

import io.swagger.v3.oas.annotations.Operation;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/blade-chat/post")
@io.swagger.v3.oas.annotations.tags.Tag(name = "小程序广告接口")
public class WeChatPostController {

	private final WeChatPostService postService;
	private final IFormConfigService formConfigService;


	/**
	 * 新增各类帖子  新增帖子可以选择性的关联自己加入的机构
	 */
	@PostMapping("/create")
	@Operation(summary = "新增帖子")
	public R<SupPost> createPost(@RequestBody PostCreateRequest createRequest) {
		return R.data(postService.createPost(createRequest));
	}

	/**
	 * 根据帖子分类Id来条件分页查询帖子
	 */
//	@GetMapping("/list/{postCategoryId}")
//	@Operation(summary = "根据分类Id来条件分页查询帖子")
//	public R<IPage<SupPostVO>> getPostListByCategoryId(@PathVariable(value = "postCategoryId", required = false)  Long postCategoryId, Query query) {
//		return R.data(postService.getPostListByCategoryId(postCategoryId, query));
//	}

//	@GetMapping("/list")
//	@Operation(summary = "根据分类Id来条件分页查询帖子")
//	public R<IPage<SupPostVO>> getPostListByCategoryId(
//		@RequestParam(value = "postCategoryId", required = false) Long postCategoryId,
//		Query query) {
//		return R.data(postService.getPostListByCategoryId(postCategoryId, query));
//	}

	/**
	 * 获取帖子列表
	 */
//	@GetMapping("/list")
//	@Operation(summary = "获取帖子列表")
//	public R<IPage<SupPostVO>> getPostList(Query query) {
//		try {
//			return R.data(postService.getPostList(query));
//		} catch (Exception e) {
//			log.error("获取帖子列表失败", e);
//			return R.fail("获取帖子列表失败");
//		}
//	}



	/**
	 * 获取我的帖子列表
	 */
	@AccessLimit(seconds = 30,maxCount = 100)
	@GetMapping("/my")
	public R<IPage<SupPostVO>> getMyPosts(Query query) {
		return R.data(postService.getMyPosts(query));
	}

	/**
	 * 获取我点赞的帖子列表
	 */
	@AccessLimit(seconds = 30)
	@GetMapping("/liked")
	public R<IPage<SupPostVO>> getLikedPosts(Query query) {
		return R.data(postService.getLikedPosts(query));
	}

	/**
	 * 获取我收藏的帖子列表
	 */
	@AccessLimit(seconds = 30,maxCount = 100)
	@GetMapping("/favorite")
	public R<IPage<SupPostVO>> getFavoritePosts(Query query) {
		return R.data(postService.getFavoritePosts(query));
	}

	/**
	 * 获取收藏标签列表
	 */
	@AccessLimit(seconds = 30, maxCount = 100)
	@GetMapping("/favorite/tags")
	@Operation(summary = "获取收藏标签列表")
	public R<List<String>> getFavoriteTags() {
		try {
			return R.data(postService.getFavoriteTags());
		} catch (Exception e) {
			log.error("获取收藏标签失败", e);
			return R.fail("获取收藏标签失败");
		}
	}

	/**
	 * 清空收藏
	 */
	@PostMapping("/favorite/clear")
	@Operation(summary = "清空收藏")
	public R<Boolean> clearFavorites() {
		try {
			return R.data(postService.clearFavorites());
		} catch (Exception e) {
			log.error("清空收藏失败", e);
			return R.fail("清空收藏失败");
		}
	}

	/**
	 * 删除帖子
	 */
	@DeleteMapping("/{id}")
	@Operation(summary = "删除帖子")
	public R<Boolean> deletePost(@PathVariable("id") Long id) {
		try {
			return R.data(postService.deletePost(id));
		} catch (Exception e) {
			log.error("删除帖子失败", e);
			return R.fail("删除帖子失败");
		}
	}

	/**
	 * 切换帖子完成状态
	 */
	@PostMapping("/completed/{id}")
	@Operation(summary = "切换帖子完成状态")
	public R<Boolean> toggleCompleted(@PathVariable("id") Long id) {
		try {
			return R.data(postService.toggleCompleted(id));
		} catch (Exception e) {
			log.error("切换帖子完成状态失败", e);
			return R.fail("切换帖子完成状态失败");
		}
	}


	@Operation(summary = "查询分类的标签")
	@GetMapping("/tag/category/{categoryId}")
	public R<List<Tag>> tags(@PathVariable("categoryId") Long id ){
		return R.data(postService.getTagsByCategory(id));
	}


	@Operation(summary = "查询分类的表单")
	@GetMapping("/form/category/{categoryId}")
	public R<FormConfigVO> form(@PathVariable("categoryId") Long id ){
		return R.data(formConfigService.getByCategoryId(id));
	}



	@Operation(summary = "查询分类的标签")
	@PostMapping("/tag/custom")
	public R tags(@RequestBody TagCreateCommand request){
		return R.status(postService.createCustomTag(request));
	}

	/**
	 * 获取浏览记录
	 */
	@GetMapping("/view-history")
	@Operation(summary = "获取浏览记录", description = "分页获取浏览记录")
	public R<IPage<SupPostVO>> getViewHistory(Query query) {
		return R.data(postService.getViewHistory(query));
	}

	/**
	 * 清空浏览记录
	 */
	@PostMapping("/clear-view-history")
	@Operation(summary = "清空浏览记录")
	public R clearViewHistory() {
		return R.status(postService.clearViewHistory());
	}

	/**
	 * 删除单条浏览记录
	 */
	@DeleteMapping("/view-history/{id}")
	@Operation(summary = "删除浏览记录")
	public R<Boolean> deleteViewHistory(@PathVariable("id") Long id) {
		try {
			return R.data(postService.deleteViewHistory(id));
		} catch (Exception e) {
			log.error("删除浏览记录失败", e);
			return R.fail("删除浏览记录失败");
		}
	}

	/**
	 * 收藏帖子
	 */
	@PostMapping("/favorite/{postId}")
	@Operation(summary = "收藏帖子")
	public R<Boolean> favoritePost(@PathVariable("postId") Long postId) {
		try {
			return R.data(postService.toggleFavorite(postId));
		} catch (Exception e) {
			log.error("收藏操作失败", e);
			return R.fail("收藏操作失败");
		}
	}

	/**
	 * 记录分享行为
	 */
	@PostMapping("/share")
	@Operation(summary = "记录分享行为")
	public R<Boolean> recordShare(@RequestParam Long postId, @RequestParam String type) {
		try {
			// 这里应调用service层方法进行分享记录，示例：
			boolean result = postService.recordShare(postId, type);
			return R.data(result);
		} catch (Exception e) {
			log.error("记录分享行为失败", e);
			return R.fail("记录分享行为失败");
		}
	}

	@PostMapping("/submit")
	@Operation(summary = "发布广告")
	public R<Boolean> submitPost(@RequestBody SupPostVO post) {
		return R.data(postService.submitPost(post));
	}

	@PostMapping("/save-draft")
	@Operation(summary = "保存草稿")
	public R<Long> saveDraft(@RequestBody SupPostVO post) {
		return R.data(postService.saveDraft(post));
	}

	@GetMapping("/draft-list")
	@Operation(summary = "获取草稿列表")
	public R<IPage<SupPostVO>> getDraftList(Query query) {
		return R.data(postService.getDraftList(query));
	}

	@GetMapping("/draft/{id}")
	@Operation(summary = "获取草稿详情")
	public R<SupPostVO> getDraftDetail(@PathVariable Long id) {
		return R.data(postService.getDraftDetail(id));
	}

	@DeleteMapping("/draft/{id}")
	@Operation(summary = "删除草稿")
	public R<Boolean> deleteDraft(@PathVariable Long id) {
		return R.data(postService.deleteDraft(id));
	}

	/**
	 * 解密-获取帖子联系方式
	 */
	@GetMapping("/post/contact/{id}")
	@Operation(summary = "获取帖子联系方式")
	public R<String> getPostContact(@PathVariable("id") Long id) {
		try {
			return R.data(postService.getContact(id));
		} catch (Exception e) {
			log.error("获取帖子联系方式失败", e);
			return R.fail("访问权限上限");
		}
	}

	/**
	 * 获取帖子详情
	 */
	@GetMapping("/{id}")
	@Operation(summary = "获取帖子详情")
	public R<SupPostVO> getPostDetail(@PathVariable("id") Long id) {
		try {
			return R.data(postService.getPostDetail(id));
		} catch (Exception e) {
			log.error("获取帖子详情失败", e);
			return R.fail("获取帖子详情失败");
		}
	}

	/**
	 * 移动帖子到草稿箱
	 */
	@PostMapping("/move-draft/{id}")
	@Operation(summary = "移动帖子到草稿箱")
	public R<Boolean> moveToDraft(@PathVariable("id") Long id) {
		try {
			return R.data(postService.moveToDraft(id));
		} catch (Exception e) {
			log.error("移动帖子到草稿箱失败", e);
			return R.fail("移动帖子到草稿箱失败");
		}
	}

	/**
	 * 从草稿箱发布帖子
	 */
	@PostMapping("/publish-draft/{id}")
	@Operation(summary = "从草稿箱发布帖子")
	public R<Boolean> publishFromDraft(@PathVariable("id") Long id) {
		try {
			return R.data(postService.publishFromDraft(id));
		} catch (Exception e) {
			log.error("从草稿箱发布帖子失败", e);
			return R.fail("从草稿箱发布帖子失败");
		}
	}

}
