/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.modules.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.system.entity.FileUpload;
import org.springblade.modules.system.service.IFileUploadService;
import org.springblade.modules.system.vo.FileUploadVO;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 文件上传控制器
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-system/file-upload")
@Tag(name = "文件上传", description = "文件上传接口")
public class FileUploadController extends BladeController {

    private final IFileUploadService fileUploadService;

    /**
     * 上传单个文件
     */
    @PostMapping("/upload")
    @Operation(summary = "上传单个文件", description = "上传单个文件到云存储")
    public R<FileUpload> uploadFile(
        @Parameter(description = "文件") @RequestParam("file") MultipartFile file,
        @Parameter(description = "上传来源") @RequestParam(value = "uploadSource", defaultValue = "admin") String uploadSource,
        @Parameter(description = "业务类型") @RequestParam(value = "businessType", required = false) String businessType,
        @Parameter(description = "业务ID") @RequestParam(value = "businessId", required = false) Long businessId
    ) {
        FileUpload fileUpload = fileUploadService.uploadFile(file, uploadSource, businessType, businessId);
        return R.data(fileUpload);
    }

    /**
     * 批量上传文件
     */
    @PostMapping("/upload-batch")
    @Operation(summary = "批量上传文件", description = "批量上传文件到云存储")
    public R<List<FileUpload>> uploadFiles(
        @Parameter(description = "文件列表") @RequestParam("files") List<MultipartFile> files,
        @Parameter(description = "上传来源") @RequestParam(value = "uploadSource", defaultValue = "admin") String uploadSource,
        @Parameter(description = "业务类型") @RequestParam(value = "businessType", required = false) String businessType,
        @Parameter(description = "业务ID") @RequestParam(value = "businessId", required = false) Long businessId
    ) {
        List<FileUpload> fileUploads = fileUploadService.uploadFiles(files, uploadSource, businessType, businessId);
        return R.data(fileUploads);
    }

    /**
     * 删除文件
     */
    @PostMapping("/remove")
    @Operation(summary = "删除文件", description = "删除文件")
    public R<Boolean> remove(@Parameter(description = "主键集合") @RequestParam String ids) {
        return R.data(fileUploadService.deleteFiles(Func.toLongList(ids)));
    }

    /**
     * 分页查询文件上传
     */
    @GetMapping("/list")
    @Operation(summary = "分页查询", description = "分页查询文件上传")
    public R<IPage<FileUploadVO>> list(FileUploadVO fileUpload, Query query) {
        IPage<FileUploadVO> pages = fileUploadService.selectFileUploadPage(Condition.getPage(query), fileUpload);
        return R.data(pages);
    }

    /**
     * 根据业务信息查询文件
     */
    @GetMapping("/business")
    @Operation(summary = "根据业务查询文件", description = "根据业务类型和业务ID查询文件")
    public R<List<FileUpload>> getFilesByBusiness(
        @Parameter(description = "业务类型") @RequestParam String businessType,
        @Parameter(description = "业务ID") @RequestParam Long businessId
    ) {
        List<FileUpload> files = fileUploadService.getFilesByBusiness(businessType, businessId);
        return R.data(files);
    }

    /**
     * 获取文件统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取统计信息", description = "获取文件上传统计信息")
    public R<Object> getStats() {
        Object stats = fileUploadService.getFileStats();
        return R.data(stats);
    }

    /**
     * 小程序上传文件接口
     */
    @PostMapping("/miniapp/upload")
    @Operation(summary = "小程序上传文件", description = "小程序上传文件接口")
    public R<FileUpload> miniappUploadFile(
        @Parameter(description = "文件") @RequestParam("file") MultipartFile file,
        @Parameter(description = "业务类型") @RequestParam(value = "businessType", required = false) String businessType,
        @Parameter(description = "业务ID") @RequestParam(value = "businessId", required = false) Long businessId
    ) {
        FileUpload fileUpload = fileUploadService.uploadFile(file, "miniapp", businessType, businessId);
        return R.data(fileUpload);
    }

    /**
     * 小程序批量上传文件接口
     */
    @PostMapping("/miniapp/upload-batch")
    @Operation(summary = "小程序批量上传文件", description = "小程序批量上传文件接口")
    public R<List<FileUpload>> miniappUploadFiles(
        @Parameter(description = "文件列表") @RequestParam("files") List<MultipartFile> files,
        @Parameter(description = "业务类型") @RequestParam(value = "businessType", required = false) String businessType,
        @Parameter(description = "业务ID") @RequestParam(value = "businessId", required = false) Long businessId
    ) {
        List<FileUpload> fileUploads = fileUploadService.uploadFiles(files, "miniapp", businessType, businessId);
        return R.data(fileUploads);
    }
}
