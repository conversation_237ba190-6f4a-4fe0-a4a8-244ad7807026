/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 名片信息表实体类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@TableName("urb_business_card")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "名片信息表")
public class BusinessCard extends BaseEntity {

    @Serial
	private static final long serialVersionUID = 1L;

    /**
     * 公司名称
     */
    @Schema(description = "公司名称")
    private String company;
    /**
     * 职位
     */
    @Schema(description = "职位")
    private String jobTitle;
    /**
     * 业务简介
     */
    @Schema(description = "业务简介")
    private String businessProfile;
    /**
     * 姓名
     */
    @Schema(description = "姓名")
    private String fullName;
    /**
     * 性别(0-保密，1-男，2-女)
     */
    @Schema(description = "性别(0-保密，1-男，2-女)")
    private Integer gender;
    /**
     * 电话
     */
    @Schema(description = "电话")
    private String phone;
    /**
     * 地址
     */
    @Schema(description = "地址")
    private String address;
    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    private String email;
    /**
     * 网址
     */
    @Schema(description = "网址")
    private String website;
    /**
     * 微信
     */
    @Schema(description = "微信")
    private String weixin;
    /**
     * 头像
     */
    @Schema(description = "头像")
    private String avatar;
    /**
     * 图片
     */
    @Schema(description = "图片")
    private String images;
    /**
     * 视频
     */
    @Schema(description = "视频")
    private String video;
    /**
     * 备注信息
     */
    @Schema(description = "备注信息")
    private String description;
    /**
     * 是否公开（0-否，1-是）
     */
    @Schema(description = "是否公开（0-否，1-是）")
    private Integer isPublic;


	@Schema(description = "审核状态（0-待审核，1-通过，2-拒绝）")
	private String auditStatus;

	@Schema
	private String publishStatus;

	/**
	 * 经度
	 */
	@Schema(description = "经度")
	private BigDecimal longitude;

	/**
	 * 纬度
	 */
	@Schema(description = "纬度")
	private BigDecimal latitude;


	private Long ownerId;


	@Schema(description = "发布时间")
	private Date publishTime;
}
