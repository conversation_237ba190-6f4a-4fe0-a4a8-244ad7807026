package org.springblade.miniapp.service.impl;

import jakarta.annotation.Resource;
import org.springblade.miniapp.service.LimitConfigService;
import org.springblade.modules.system.service.IParamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class LimitConfigServiceImpl implements LimitConfigService {
	@Resource
	private IParamService paramService;
    @Override
    public Integer getLimit(String key) {
		//把参数值转换为整数
		String limitStr = paramService.getByKey(key);
		Integer limit = null;
		if (limitStr != null && !limitStr.trim().isEmpty()) {
			try {
				limit = Integer.parseInt(limitStr.trim());
			} catch (NumberFormatException e) {
				// 这里可以记录日志或给默认值
				limit = 10; // 默认值
			}
		} else {
			limit = 10; // 默认值
		}
        return limit; // 或者直接返回默认值
    }
}
