package org.springblade.common.constant.bizz;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 联系方式美剧
 *
 * <AUTHOR>
 * @version 1.0
 * @email <EMAIL>
 * @website duofan.top
 * @date 2025/7/26
 */
@Getter
@AllArgsConstructor
public enum ContactTypeEnum {
    /** 新增帖子 */
    PHONE("0", "手机"),
    WECHAT("1", "微信"),
    // 可继续扩展其他类型
    ;

    private final String code;
    private final String desc;
}
