/*
SQLyog Community v13.3.0 (64 bit)
MySQL - 8.4.5 : Database - adb-management
*********************************************************************
*/

/*!40101 SET NAMES utf8 */;

/*!40101 SET SQL_MODE=''*/;

/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
/*Table structure for table `urb_audit_post` */

DROP TABLE IF EXISTS `urb_audit_post`;

CREATE TABLE `urb_audit_post` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `post_id` bigint DEFAULT NULL COMMENT '信息贴ID',
  `audit_time` datetime(6) DEFAULT NULL COMMENT '审核时间',
  `audit_status` varchar(20) DEFAULT NULL COMMENT '审核状态',
  `audit_user` bigint DEFAULT NULL COMMENT '审核人ID',
  `audit_remark` text COMMENT '审核备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='审核日志';

/*Table structure for table `urb_audit_rule` */

DROP TABLE IF EXISTS `urb_audit_rule`;

CREATE TABLE `urb_audit_rule` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `rule_name` varchar(50) DEFAULT NULL COMMENT '规则名称',
  `keywords` json DEFAULT NULL COMMENT '违禁词列表',
  `max_content_length` int DEFAULT '0' COMMENT '最大内容长度',
  `enable_image_check` tinyint(1) DEFAULT '0' COMMENT '启用图片审核',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='审核规则';

/*Table structure for table `urb_business_card` */

DROP TABLE IF EXISTS `urb_business_card`;

CREATE TABLE `urb_business_card` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '名片ID',
  `company` varchar(100) DEFAULT NULL COMMENT '公司名称',
  `job_title` varchar(50) DEFAULT NULL COMMENT '职位',
  `business_profile` text COMMENT '业务简介',
  `full_name` varchar(30) NOT NULL COMMENT '姓名',
  `gender` int DEFAULT '0' COMMENT '性别(0-保密，1-男，2-女)',
  `phone` varchar(20) DEFAULT NULL COMMENT '电话',
  `address` varchar(100) DEFAULT NULL COMMENT '地址',
  `email` varchar(50) DEFAULT NULL COMMENT '邮箱',
  `website` varchar(100) DEFAULT NULL COMMENT '网址',
  `weixin` varchar(50) DEFAULT NULL COMMENT '微信',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `images` text COMMENT '图片',
  `video` text COMMENT '视频',
  `description` text COMMENT '备注信息',
  `is_public` int DEFAULT '1' COMMENT '是否公开（0-否，1-是）',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `create_dept` bigint DEFAULT NULL,
  `create_user` bigint DEFAULT NULL,
  `update_user` bigint DEFAULT NULL,
  `status` int DEFAULT NULL,
  `is_deleted` int DEFAULT '0' COMMENT '是否删除',
  `audit_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '0' COMMENT '审核状态（0-待审核，1-已通过，2-未通过)',
  `card_type` varchar(20) DEFAULT NULL COMMENT '名片类型',
  `longitude` decimal(10,6) DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10,6) DEFAULT NULL COMMENT '纬度',
  `owner_id` bigint DEFAULT NULL COMMENT '所有者id',
  `publish_status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '1' COMMENT '发布状态',
  `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
  PRIMARY KEY (`id`),
  KEY `idx_location` (`latitude`,`longitude`),
  KEY `idx_public_audit` (`is_public`,`audit_status`)
) ENGINE=InnoDB AUTO_INCREMENT=1951168115186855938 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='名片信息表';

/*Table structure for table `urb_business_card_favorite_ext` */

DROP TABLE IF EXISTS `urb_business_card_favorite_ext`;

CREATE TABLE `urb_business_card_favorite_ext` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `card_id` bigint NOT NULL COMMENT '名片ID',
  `user_id` bigint NOT NULL COMMENT '收藏用户ID',
  `card_snapshot` longtext COMMENT '名片快照数据（JSON格式）',
  `category` varchar(50) DEFAULT NULL COMMENT '收藏分类',
  `remark` varchar(500) DEFAULT NULL COMMENT '收藏备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_card_user_ext` (`card_id`,`user_id`),
  KEY `idx_card_id` (`card_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='名片收藏扩展信息表';

/*Table structure for table `urb_category` */

DROP TABLE IF EXISTS `urb_category`;

CREATE TABLE `urb_category` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `name` varchar(50) DEFAULT NULL COMMENT '分类名称',
  `parent_id` bigint DEFAULT NULL COMMENT '上级分类ID',
  `icon` varchar(200) DEFAULT NULL COMMENT '分类图标',
  `description` varchar(500) DEFAULT NULL COMMENT '分类描述',
  `sort` int DEFAULT '0' COMMENT '排序',
  `enabled` int DEFAULT '1' COMMENT '是否启用：0-否，1-是',
  `enable_audit` int DEFAULT '1' COMMENT '是否启用审核：0-否，1-是',
  `tip` text COMMENT '提示语言',
  `sort_order` int DEFAULT NULL COMMENT '顺序',
  `max_images` int DEFAULT NULL,
  `allow_tags` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_sort` (`sort`)
) ENGINE=InnoDB AUTO_INCREMENT=1949363715824844803 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='广告分类';

/*Table structure for table `urb_comment_like` */

DROP TABLE IF EXISTS `urb_comment_like`;

CREATE TABLE `urb_comment_like` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `comment_id` bigint NOT NULL COMMENT '评论ID',
  `like_type` varchar(20) DEFAULT 'HELPFUL' COMMENT '点赞类型：HELPFUL-有帮助，LIKE-点赞',
  `create_time` datetime NOT NULL,
  `update_time` datetime(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_user_feedback` (`user_id`,`comment_id`)
) ENGINE=InnoDB AUTO_INCREMENT=107 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/*Table structure for table `urb_comment_mention` */

DROP TABLE IF EXISTS `urb_comment_mention`;

CREATE TABLE `urb_comment_mention` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `comment_id` bigint NOT NULL COMMENT '评论ID',
  `mentioned_user_id` bigint NOT NULL COMMENT '被提及的用户ID',
  `mentioned_user_name` varchar(100) NOT NULL COMMENT '被提及的用户昵称',
  `create_time` datetime(6) DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_comment_user` (`comment_id`,`mentioned_user_id`),
  KEY `idx_comment_id` (`comment_id`),
  KEY `idx_mentioned_user_id` (`mentioned_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='评论提及表';

/*Table structure for table `urb_comment_report` */

DROP TABLE IF EXISTS `urb_comment_report`;

CREATE TABLE `urb_comment_report` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `comment_id` bigint NOT NULL COMMENT '评论ID',
  `reporter_user_id` bigint NOT NULL COMMENT '举报用户ID',
  `report_reason` varchar(100) NOT NULL COMMENT '举报原因',
  `report_content` text COMMENT '举报详细内容',
  `report_status` varchar(20) DEFAULT 'PENDING' COMMENT '举报状态：PENDING-待处理，APPROVED-已处理，REJECTED-已驳回',
  `process_user_id` bigint DEFAULT NULL COMMENT '处理人ID',
  `process_time` datetime(6) DEFAULT NULL COMMENT '处理时间',
  `process_remark` text COMMENT '处理备注',
  `create_time` datetime(6) DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `update_time` datetime(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  PRIMARY KEY (`id`),
  KEY `idx_comment_id` (`comment_id`),
  KEY `idx_reporter_user_id` (`reporter_user_id`),
  KEY `idx_report_status` (`report_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='评论举报表';

/*Table structure for table `urb_contact` */

DROP TABLE IF EXISTS `urb_contact`;

CREATE TABLE `urb_contact` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `name` varchar(50) DEFAULT NULL COMMENT '联系人姓名',
  `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `wechat` varchar(50) DEFAULT NULL COMMENT '微信账号',
  `contact_type` varchar(20) DEFAULT NULL COMMENT '联系方式类型',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1951207632571772930 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='联系人';

/*Table structure for table `urb_contact_access` */

DROP TABLE IF EXISTS `urb_contact_access`;

CREATE TABLE `urb_contact_access` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联记录唯一标识',
  `user_id` bigint NOT NULL COMMENT '关联的用户ID（外键）',
  `post_id` bigint NOT NULL COMMENT '关联的帖子ID（外键）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '关联关系创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/*Table structure for table `urb_cooperation_leads` */

DROP TABLE IF EXISTS `urb_cooperation_leads`;

CREATE TABLE `urb_cooperation_leads` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '唯一标识ID',
  `name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系人姓名',
  `phone` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系电话',
  `wechat` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '微信ID',
  `cooperation_type` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '合作类型(广告/赞助/联合推广等)',
  `cooperation_details` text COLLATE utf8mb4_general_ci COMMENT '合作详情描述',
  `status` int DEFAULT '0' COMMENT '状态(0-待处理，1-已联系，2已合作，3-已拒绝)',
  `remarks` text COLLATE utf8mb4_general_ci COMMENT '备注信息',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除(软删除)',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `create_dept` bigint DEFAULT NULL,
  `create_user` bigint DEFAULT NULL,
  `update_user` bigint DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1950131306709381122 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='合作线索表';

/*Table structure for table `urb_coupon` */

DROP TABLE IF EXISTS `urb_coupon`;

CREATE TABLE `urb_coupon` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `exchange_id` bigint NOT NULL COMMENT '兑换记录ID',
  `goods_id` bigint NOT NULL COMMENT '商品ID',
  `coupon_code` varchar(64) NOT NULL COMMENT '优惠券码',
  `status` int DEFAULT '0' COMMENT '状态（0未使用，1已使用，2已过期）',
  `valid_from` datetime DEFAULT NULL COMMENT '有效期起',
  `valid_to` datetime DEFAULT NULL COMMENT '有效期止',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户',
  `is_deleted` int DEFAULT '0' COMMENT '0为未删除 1为删除',
  `update_user` bigint DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1948594953336168451 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户优惠券表';

/*Table structure for table `urb_favorite` */

DROP TABLE IF EXISTS `urb_favorite`;

CREATE TABLE `urb_favorite` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `relevancy_id` bigint DEFAULT NULL COMMENT '关联id',
  `type` varchar(20) DEFAULT '0' COMMENT '0-帖子，1-个人名片，2-机构',
  `user_id` bigint DEFAULT NULL COMMENT '操作用户ID',
  `fav_time` datetime(6) DEFAULT (now()) COMMENT '收藏时间',
  `is_deleted` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_relevancy_type_user` (`relevancy_id`,`type`,`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=79 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='收藏记录';

/*Table structure for table `urb_feedback` */

DROP TABLE IF EXISTS `urb_feedback`;

CREATE TABLE `urb_feedback` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `relevancy_id` bigint DEFAULT NULL COMMENT '信息贴ID',
  `star` int DEFAULT NULL COMMENT '评论星级',
  `parent_id` bigint DEFAULT NULL COMMENT '父评论ID，NULL表示顶级评论',
  `reply_to_user_id` bigint DEFAULT NULL COMMENT '被回复的用户ID',
  `reply_to_user_name` varchar(100) DEFAULT NULL COMMENT '被回复的用户昵称（冗余字段，便于显示）',
  `comment_type` varchar(20) DEFAULT 'FEEDBACK' COMMENT '评论类型：FEEDBACK-反馈，COMMENT-评论，REPLY-回复',
  `level` int DEFAULT '1' COMMENT '评论层级，1为顶级评论',
  `user_id` bigint DEFAULT NULL COMMENT '反馈用户ID',
  `content` text COMMENT '反馈内容',
  `image` varchar(500) DEFAULT NULL COMMENT '评论图片URL',
  `contact_info` varchar(255) DEFAULT NULL COMMENT '联系方式',
  `audit_status` varchar(32) DEFAULT '0' COMMENT '审核状态',
  `reason` varchar(255) DEFAULT NULL COMMENT '标签',
  `audit_time` datetime DEFAULT NULL,
  `audit_user_id` bigint DEFAULT NULL,
  `reply_count` int DEFAULT '0' COMMENT '回复数量',
  `is_author` varchar(10) DEFAULT '0' COMMENT '是否是作者（0-非，1-是）',
  `type` varchar(20) DEFAULT '0' COMMENT '关联类型(0-帖子 1-机构)',
  `is_anonymous` varchar(20) DEFAULT '0' COMMENT '是否匿名(0为不 1为是)',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_reply_to_user_id` (`reply_to_user_id`),
  KEY `idx_comment_type` (`comment_type`),
  KEY `idx_level` (`level`),
  KEY `idx_post_parent` (`relevancy_id`,`parent_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1951209926215352323 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户反馈';

/*Table structure for table `urb_feedback_tag` */

DROP TABLE IF EXISTS `urb_feedback_tag`;

CREATE TABLE `urb_feedback_tag` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `label` varchar(50) DEFAULT NULL COMMENT '标签名称',
  `description` text COMMENT '标签说明',
  `category_id` bigint DEFAULT NULL,
  `sort_order` int DEFAULT '0' COMMENT '排序序号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='反馈标签';

/*Table structure for table `urb_group_category` */

DROP TABLE IF EXISTS `urb_group_category`;

CREATE TABLE `urb_group_category` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `category_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `category_image` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分类图片',
  `sort_order` int DEFAULT NULL COMMENT '排序',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `create_dept` bigint DEFAULT NULL,
  `create_user` bigint DEFAULT NULL,
  `update_user` bigint DEFAULT NULL,
  `status` int DEFAULT NULL,
  `is_deleted` int DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1949085562103578626 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='群分类表';

/*Table structure for table `urb_group_info` */

DROP TABLE IF EXISTS `urb_group_info`;

CREATE TABLE `urb_group_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '群唯一标识',
  `group_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '群名称',
  `group_type` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '群类型',
  `group_image` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '群图片',
  `group_weixin` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '微信链接',
  `group_desc` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '群描述信息',
  `create_time` datetime DEFAULT NULL COMMENT '群创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '群信息更新时间',
  `create_dept` bigint DEFAULT NULL,
  `create_user` bigint DEFAULT NULL,
  `update_user` bigint DEFAULT NULL,
  `status` int DEFAULT NULL,
  `is_deleted` int DEFAULT '0' COMMENT '是否删除',
  `region_code` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '地区编号',
  `sort_order` int DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1949088115654553602 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='群信息表';

/*Table structure for table `urb_institution` */

DROP TABLE IF EXISTS `urb_institution`;

CREATE TABLE `urb_institution` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT (now()) COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `name` varchar(100) NOT NULL COMMENT '机构名称',
  `logo` text COMMENT '机构Logo URL或Base64',
  `type_id` bigint NOT NULL COMMENT '机构分类ID',
  `years_in_business` int DEFAULT NULL COMMENT '营业年限',
  `description` varchar(500) DEFAULT NULL COMMENT '机构简介',
  `contact_person` varchar(50) NOT NULL COMMENT '联系人姓名',
  `phone` varchar(20) NOT NULL COMMENT '联系电话',
  `backup_phone` varchar(20) DEFAULT NULL COMMENT '备用电话',
  `email` varchar(100) DEFAULT NULL COMMENT '电子邮箱',
  `wechat` varchar(50) DEFAULT NULL COMMENT '微信',
  `qq` varchar(20) DEFAULT NULL COMMENT 'QQ',
  `license_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '营业执照号码',
  `license_image` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '营业执照照片',
  `legal_person` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '法人代表姓名',
  `industry_license` text COMMENT '行业许可证',
  `tax_certificate` text COMMENT '税务登记证',
  `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '省',
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '市',
  `district` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '区',
  `detail_address` varchar(255) NOT NULL COMMENT '详细地址',
  `longitude` decimal(10,6) DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10,6) DEFAULT NULL COMMENT '纬度',
  `is_store` tinyint(1) DEFAULT '1' COMMENT '是否支持到店(1:是,0:否)',
  `service_radius` int DEFAULT NULL COMMENT '服务半径(米)',
  `has_delivery` tinyint(1) DEFAULT '0' COMMENT '是否支持外卖',
  `payment_methods` varchar(255) CHARACTER SET utf16le COLLATE utf16le_general_ci DEFAULT NULL COMMENT '支付方式(逗号分隔)',
  `special_services` varchar(255) DEFAULT NULL COMMENT '特色服务',
  `username` varchar(50) DEFAULT NULL COMMENT '登录账号',
  `password` varchar(255) DEFAULT NULL COMMENT '密码(加密)',
  `salt` varchar(50) DEFAULT NULL COMMENT '加密盐值',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `is_locked` tinyint(1) DEFAULT '0' COMMENT '账号是否锁定',
  `business_hours` text COMMENT '营业时间(JSON格式: {"monday":{"open":"09:00","close":"21:00"},...})',
  `images` text COMMENT '商家图片(JSON格式: [{"url":"...","type":1,"sort":0},...])',
  `audit_status` int NOT NULL DEFAULT '0' COMMENT '审核状态（0-待审核，1-通过，2-拒绝）',
  `audit_remark` varchar(255) DEFAULT NULL COMMENT '最后审核备注',
  `apply_user_id` bigint DEFAULT NULL COMMENT '申请人ID',
  `apply_time` datetime(6) DEFAULT NULL COMMENT '申请时间',
  `last_audit_time` datetime(6) DEFAULT NULL COMMENT '最后审核时间',
  `last_audit_user_id` bigint DEFAULT NULL COMMENT '最后审核人ID',
  `top` varchar(32) DEFAULT '0' COMMENT '是否置顶',
  `region_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '区域编码',
  `publish_status` varchar(32) DEFAULT NULL COMMENT '发布状态',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_license_no` (`license_no`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_type` (`type_id`),
  KEY `idx_status` (`status`),
  KEY `idx_location` (`longitude`,`latitude`),
  KEY `idx_tenant` (`tenant_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1951247032249655299 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='机构主表';

/*Table structure for table `urb_institution_audit_log` */

DROP TABLE IF EXISTS `urb_institution_audit_log`;

CREATE TABLE `urb_institution_audit_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `institution_id` bigint NOT NULL COMMENT '机构Id',
  `audit_user_id` bigint NOT NULL COMMENT '审核人',
  `audit_time` datetime(6) DEFAULT (now()) COMMENT '审核时间',
  `audit_status` int DEFAULT NULL COMMENT '审核状态 0为待审核 1为通过 2为拒绝',
  `audit_remark` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审核备注',
  `action` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作类型（申请/审核/修改/禁用等）',
  `create_user` bigint DEFAULT NULL,
  `create_time` datetime(6) DEFAULT (now()),
  `create_dept` bigint DEFAULT NULL,
  `is_deleted` int DEFAULT '0',
  `status` int DEFAULT '1',
  `update_user` bigint DEFAULT NULL,
  `update_time` datetime(6) DEFAULT (now()),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1950746593090617347 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='机构审核日志表';

/*Table structure for table `urb_institution_type` */

DROP TABLE IF EXISTS `urb_institution_type`;

CREATE TABLE `urb_institution_type` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `icon` text COMMENT '分类图标',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  PRIMARY KEY (`id`),
  KEY `idx_tenant` (`tenant_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1950529554896805890 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='机构分类表';

/*Table structure for table `urb_like` */

DROP TABLE IF EXISTS `urb_like`;

CREATE TABLE `urb_like` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `relevancy_id` bigint DEFAULT NULL COMMENT '关联id',
  `type` varchar(20) NOT NULL DEFAULT '0' COMMENT '点赞类型(0-帖子，1-个人名片，2-机构）',
  `user_id` bigint DEFAULT NULL COMMENT '操作用户ID',
  `like_time` datetime(6) DEFAULT (now()) COMMENT '点赞时间',
  `is_deleted` varchar(32) DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_relevancy_type_user` (`relevancy_id`,`type`,`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=91 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='点赞记录';

/*Table structure for table `urb_menu` */

DROP TABLE IF EXISTS `urb_menu`;

CREATE TABLE `urb_menu` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '唯一，菜单名称',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图片地址',
  `sort_weight` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '排序权重',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `color` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用于存储16进制颜色数据',
  `url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用于存储链接地址',
  `create_user` mediumtext COLLATE utf8mb4_unicode_ci COMMENT '创建人',
  `update_user` mediumtext COLLATE utf8mb4_unicode_ci COMMENT '修改人',
  `create_dept` mediumtext COLLATE utf8mb4_unicode_ci COMMENT '创建部门',
  `is_deleted` int DEFAULT NULL COMMENT '是否删除，0-未删除，1-已删除',
  `status` int DEFAULT NULL COMMENT '状态，0-禁用，1-启用',
  `category` int DEFAULT NULL COMMENT '0-菜单，1-滚动图片',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=1950963862350491651 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

/*Table structure for table `urb_message` */

DROP TABLE IF EXISTS `urb_message`;

CREATE TABLE `urb_message` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '消息ID',
  `user_id` bigint DEFAULT NULL COMMENT '接收用户ID',
  `title` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '消息标题',
  `content` text COLLATE utf8mb4_general_ci COMMENT '消息内容',
  `message_type` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '消息类型代码',
  `is_read` tinyint(1) DEFAULT '0' COMMENT '是否已读',
  `related_id` bigint DEFAULT NULL COMMENT '关联业务ID（如帖子ID、审核记录ID等）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1951208488071376899 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='系统消息表';

/*Table structure for table `urb_notice` */

DROP TABLE IF EXISTS `urb_notice`;

CREATE TABLE `urb_notice` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `title` varchar(100) NOT NULL COMMENT '公告标题',
  `content` text NOT NULL COMMENT '公告内容',
  `status` tinyint DEFAULT '1' COMMENT '状态(1:发布中,2:已过期,3:已撤回)',
  `create_user` bigint NOT NULL COMMENT '发布人ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `create_dept` mediumtext COMMENT '创建部门',
  `update_user` mediumtext COMMENT '更新人ID',
  `is_deleted` int DEFAULT NULL COMMENT '是否删除',
  `tag` varchar(100) DEFAULT NULL COMMENT '分类',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1942886633188151298 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='通知公告表';

/*Table structure for table `urb_pain_point` */

DROP TABLE IF EXISTS `urb_pain_point`;

CREATE TABLE `urb_pain_point` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `content` text COLLATE utf8mb4_general_ci COMMENT '反馈内容',
  `image` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '图片路径或链接',
  `feedback_time` datetime DEFAULT NULL COMMENT '反馈时间',
  `contact_info` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '联系方式',
  `create_user` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
  `update_user` bigint DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `status` tinyint DEFAULT NULL COMMENT '业务状态',
  `is_deleted` tinyint DEFAULT '0' COMMENT '是否已删除',
  `audit_status` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '处理状态',
  `audit_result` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '处理结果',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1943277053747351555 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Table structure for table `urb_points_exchange` */

DROP TABLE IF EXISTS `urb_points_exchange`;

CREATE TABLE `urb_points_exchange` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `open_id` varchar(100) DEFAULT NULL COMMENT '用户OpenID',
  `goods_id` bigint NOT NULL COMMENT '商品ID',
  `goods_name` varchar(100) DEFAULT NULL COMMENT '商品名称',
  `quantity` int DEFAULT '1' COMMENT '兑换数量',
  `points_cost` int NOT NULL COMMENT '消耗积分',
  `exchange_time` datetime(6) DEFAULT NULL COMMENT '兑换时间',
  `status` int DEFAULT '0' COMMENT '状态（0：待处理，1：处理中，2：已完成，3：已取消）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  PRIMARY KEY (`id`),
  KEY `idx_open_id` (`open_id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_exchange_time` (`exchange_time`),
  KEY `idx_status` (`status`),
  KEY `idx_points_exchange_open_id_status` (`open_id`,`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1948594953000624131 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='积分兑换记录表';

/*Table structure for table `urb_points_goods` */

DROP TABLE IF EXISTS `urb_points_goods`;

CREATE TABLE `urb_points_goods` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `goods_name` varchar(100) NOT NULL COMMENT '商品名称',
  `goods_desc` text COMMENT '商品描述',
  `goods_image` varchar(500) DEFAULT NULL COMMENT '商品图片',
  `points_price` int NOT NULL COMMENT '积分价格',
  `original_price` decimal(10,2) DEFAULT NULL COMMENT '原价',
  `stock` int DEFAULT '0' COMMENT '库存数量',
  `exchange_limit` int DEFAULT '0' COMMENT '兑换限制（0：无限制）',
  `category` varchar(50) DEFAULT NULL COMMENT '商品分类',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `status` int DEFAULT '1' COMMENT '状态（0：下架，1：上架）',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category`),
  KEY `idx_points_price` (`points_price`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_points_goods_category_status` (`category`,`status`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='积分商城商品表';

/*Table structure for table `urb_points_record` */

DROP TABLE IF EXISTS `urb_points_record`;

CREATE TABLE `urb_points_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` varchar(100) NOT NULL,
  `points` int NOT NULL COMMENT '积分变动数量（1数为获得，0数为消费）',
  `before_points` int DEFAULT NULL COMMENT '变动前积分',
  `after_points` int DEFAULT NULL COMMENT '变动后积分',
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '积分类型（0：签到，1：兑换，2：分享，3：邀请，4：管理员操作，5：抽奖）',
  `type_name` varchar(50) DEFAULT NULL COMMENT '积分类型名称',
  `business_id` bigint DEFAULT NULL COMMENT '关联业务ID',
  `business_type` varchar(50) DEFAULT NULL COMMENT '关联业务类型',
  `description` varchar(200) DEFAULT NULL COMMENT '描述',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `operate_time` datetime(6) DEFAULT NULL COMMENT '操作时间',
  `operator` varchar(100) DEFAULT NULL COMMENT '操作人',
  `status` int DEFAULT '1' COMMENT '状态（0：无效，1：有效）',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  PRIMARY KEY (`id`),
  KEY `idx_open_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_operate_time` (`operate_time`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_points_record_open_id_type` (`user_id`,`type`)
) ENGINE=InnoDB AUTO_INCREMENT=1950950293714198531 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='积分记录表';

/*Table structure for table `urb_post` */

DROP TABLE IF EXISTS `urb_post`;

CREATE TABLE `urb_post` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `title` varchar(255) DEFAULT NULL COMMENT '标题',
  `content` text COMMENT '内容',
  `images` text COMMENT '图片',
  `address` varchar(255) DEFAULT NULL COMMENT '发布地址',
  `publish_time` varchar(255) DEFAULT NULL COMMENT '发布时间',
  `audit_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '0' COMMENT '审核状态',
  `geo_location` json DEFAULT NULL COMMENT '地理位置',
  `tags` varchar(128) DEFAULT NULL COMMENT '标签',
  `contact_name` varchar(32) DEFAULT NULL COMMENT '联系人',
  `completed` int DEFAULT '0' COMMENT '是否已完成',
  `category_id` bigint DEFAULT NULL COMMENT '分类ID',
  `audit_remark` varchar(500) DEFAULT NULL COMMENT '审核备注',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_user_id` bigint DEFAULT NULL COMMENT '审核人ID',
  `enable_audit` int DEFAULT '1' COMMENT '是否启用审核：0-否，1-是',
  `top` varchar(32) DEFAULT '0' COMMENT '是否置顶',
  `publish_status` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '1' COMMENT '发布状态 0为未发布 1为已发布 2已下架',
  `time_status` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时效状态 0为未完成 1为已完成',
  `description` varchar(200) DEFAULT NULL COMMENT '描述',
  `contact_type` varchar(50) DEFAULT NULL COMMENT '联系方式类型',
  `contact_phone` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '联系手机号',
  `location` varchar(200) DEFAULT NULL COMMENT '地址',
  `longitude` decimal(10,6) DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10,6) DEFAULT NULL COMMENT '纬度',
  `is_anonymity` int DEFAULT '0' COMMENT '是否匿名（0-否，1-是）',
  `business_type` varchar(20) NOT NULL DEFAULT 'normal' COMMENT '帖子类型（normal-普通，carpool-顺风车，jobSeeking-求职，jobOffer-招人）',
  `institution_id` bigint DEFAULT NULL COMMENT '关联机构id',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_audit_status` (`audit_status`),
  KEY `idx_publish_time` (`publish_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1951208487551283203 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='百事通信息贴';

/*Table structure for table `urb_report` */

DROP TABLE IF EXISTS `urb_report`;

CREATE TABLE `urb_report` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `post_id` bigint DEFAULT NULL COMMENT '信息贴ID',
  `user_id` bigint DEFAULT NULL COMMENT '举报用户ID',
  `content` text COMMENT '举报内容',
  `images` text COMMENT '举报图片',
  `reason` varchar(255) DEFAULT NULL COMMENT '标签',
  `audit_status` varchar(32) DEFAULT NULL COMMENT '审核状态 0未审核  1审核成功  2未审核失败',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1949727049615253506 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='举报记录';

/*Table structure for table `urb_report_tag` */

DROP TABLE IF EXISTS `urb_report_tag`;

CREATE TABLE `urb_report_tag` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `label` varchar(50) DEFAULT NULL COMMENT '标签名称',
  `description` text COMMENT '标签说明',
  `sort_order` int DEFAULT '0' COMMENT '排序序号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='举报标签';

/*Table structure for table `urb_share_log` */

DROP TABLE IF EXISTS `urb_share_log`;

CREATE TABLE `urb_share_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint DEFAULT NULL COMMENT '分享用户ID',
  `post_id` bigint DEFAULT NULL COMMENT '信息贴ID',
  `share_time` datetime DEFAULT NULL COMMENT '分享时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=40 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Table structure for table `urb_signin_record` */

DROP TABLE IF EXISTS `urb_signin_record`;

CREATE TABLE `urb_signin_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户ID',
  `signin_date` date NOT NULL COMMENT '签到日期',
  `signin_time` datetime(6) DEFAULT NULL COMMENT '签到时间',
  `year` int DEFAULT NULL COMMENT '签到年份',
  `month` int DEFAULT NULL COMMENT '签到月份',
  `day` int DEFAULT NULL COMMENT '签到日',
  `week_day` int DEFAULT NULL COMMENT '签到星期（1-7，对应周一到周日）',
  `points` int DEFAULT '0' COMMENT '获得积分',
  `continuous_reward` int DEFAULT '0' COMMENT '连续签到奖励积分',
  `continuous_days` int DEFAULT '0' COMMENT '连续签到天数',
  `signin_type` varchar(20) DEFAULT 'NORMAL' COMMENT '签到类型（NORMAL：正常签到，MAKEUP：补签）',
  `signin_ip` varchar(50) DEFAULT NULL COMMENT '签到IP',
  `device_info` varchar(500) DEFAULT NULL COMMENT '签到设备信息',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `status` int DEFAULT '1' COMMENT '状态（0：无效，1：有效）',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_open_id_date` (`user_id`,`signin_date`),
  KEY `idx_open_id` (`user_id`),
  KEY `idx_signin_date` (`signin_date`),
  KEY `idx_year_month` (`year`,`month`),
  KEY `idx_signin_type` (`signin_type`),
  KEY `idx_continuous_days` (`continuous_days`),
  KEY `idx_signin_record_open_id_date` (`user_id`,`signin_date`)
) ENGINE=InnoDB AUTO_INCREMENT=1951184106113404930 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='签到记录表';

/*Table structure for table `urb_signin_reward_config` */

DROP TABLE IF EXISTS `urb_signin_reward_config`;

CREATE TABLE `urb_signin_reward_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `days` int NOT NULL COMMENT '连续签到天数',
  `reward_points` int NOT NULL COMMENT '奖励积分',
  `reward_desc` varchar(200) DEFAULT NULL COMMENT '奖励描述',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_days` (`days`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='连续签到奖励配置表';

/*Table structure for table `urb_tag` */

DROP TABLE IF EXISTS `urb_tag`;

CREATE TABLE `urb_tag` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `tag_name` varchar(50) DEFAULT NULL COMMENT '标签名称',
  `description` text COMMENT '描述说明',
  `sort_order` int DEFAULT '0' COMMENT '排序序号',
  `name` varchar(50) DEFAULT NULL COMMENT '标签名称',
  `category_id` bigint DEFAULT NULL COMMENT '分类id',
  `color` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '颜色',
  `icon` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '图标',
  `sort` int DEFAULT NULL COMMENT '排序',
  `enabled` int DEFAULT '1' COMMENT '是否启用',
  `use_count` int DEFAULT NULL COMMENT '使用次数',
  `is_system` int DEFAULT NULL COMMENT '是否系统标签',
  `type` int DEFAULT NULL COMMENT '标签类别',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_use_count` (`use_count`),
  KEY `idx_sort` (`sort`)
) ENGINE=InnoDB AUTO_INCREMENT=1951191367867146242 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='信息标签';

/*Table structure for table `urb_user` */

DROP TABLE IF EXISTS `urb_user`;

CREATE TABLE `urb_user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `mobile` varchar(20) DEFAULT NULL COMMENT '手机号',
  `gender` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '1' COMMENT '性别',
  `signature` text COMMENT '个性签名',
  `avatar` varchar(1024) DEFAULT NULL COMMENT '头像',
  `birthday` varchar(255) DEFAULT NULL,
  `region` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `age` int DEFAULT '25' COMMENT '年龄',
  `balance` int DEFAULT '0' COMMENT '积分',
  `company_name` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1950379339993989122 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户信息';

/*Table structure for table `urb_user_institution` */

DROP TABLE IF EXISTS `urb_user_institution`;

CREATE TABLE `urb_user_institution` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `institution_id` bigint NOT NULL COMMENT '机构ID',
  `role` varchar(32) COLLATE utf8mb4_general_ci DEFAULT 'member' COMMENT '成员角色（member/admin/owner）',
  `status` int DEFAULT '1' COMMENT '状态（1正常，0禁用，2待审核）',
  `create_time` datetime(6) NOT NULL DEFAULT (now()) COMMENT '关联创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1951247032639725571 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户与机构关联表';

/*Table structure for table `urb_view_log` */

DROP TABLE IF EXISTS `urb_view_log`;

CREATE TABLE `urb_view_log` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `relevancy_id` bigint DEFAULT NULL COMMENT '关联ID',
  `user_id` bigint DEFAULT NULL COMMENT '浏览用户ID',
  `view_time` datetime(6) DEFAULT (now()) COMMENT '浏览时间',
  `ip` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'ip',
  `is_deleted` varchar(32) DEFAULT '0',
  `type` varchar(20) DEFAULT NULL COMMENT '类型（0-帖子浏览，1-个人名片，2-机构浏览）',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1537 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='浏览记录';

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
