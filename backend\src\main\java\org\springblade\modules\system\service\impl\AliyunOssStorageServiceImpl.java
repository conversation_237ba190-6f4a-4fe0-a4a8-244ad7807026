/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.modules.system.service.impl;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.system.config.CloudStorageConfig;
import org.springblade.modules.system.service.ICloudStorageService;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;

/**
 * 阿里云OSS存储服务实现
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Slf4j
@Service("aliyunOssStorageService")
@RequiredArgsConstructor
public class AliyunOssStorageServiceImpl implements ICloudStorageService {

    private final CloudStorageConfig config;

    @Override
    public String uploadFile(MultipartFile file, String path) {
        try {
            OSS ossClient = createOssClient();
            PutObjectRequest putObjectRequest = new PutObjectRequest(
                config.getAliyunOss().getBucketName(),
                path,
                file.getInputStream()
            );
            ossClient.putObject(putObjectRequest);
            ossClient.shutdown();
            return getFileUrl(path);
        } catch (Exception e) {
            log.error("阿里云OSS上传文件失败", e);
            throw new RuntimeException("文件上传失败", e);
        }
    }

    @Override
    public String uploadFile(InputStream inputStream, String fileName, String path, String contentType) {
        try {
            OSS ossClient = createOssClient();
            PutObjectRequest putObjectRequest = new PutObjectRequest(
                config.getAliyunOss().getBucketName(),
                path,
                inputStream
            );
			ObjectMetadata metadata = new ObjectMetadata();
			metadata.setContentType(contentType);
			putObjectRequest.setMetadata(metadata);
//            putObjectRequest.setMetadata(com.aliyun.oss.model.ObjectMetadata.builder()
//                .contentType(contentType)
//                .build());
            ossClient.putObject(putObjectRequest);
            ossClient.shutdown();
            return getFileUrl(path);
        } catch (Exception e) {
            log.error("阿里云OSS上传文件失败", e);
            throw new RuntimeException("文件上传失败", e);
        }
    }

    @Override
    public String uploadFile(byte[] data, String fileName, String path, String contentType) {
        try {
            OSS ossClient = createOssClient();
            PutObjectRequest putObjectRequest = new PutObjectRequest(
                config.getAliyunOss().getBucketName(),
                path,
                new ByteArrayInputStream(data)
            );
			ObjectMetadata metadata = new ObjectMetadata();
			metadata.setContentType(contentType);
			putObjectRequest.setMetadata(metadata);
//            putObjectRequest.setMetadata(com.aliyun.oss.model.ObjectMetadata.builder()
//                .contentType(contentType)
//                .build());
            ossClient.putObject(putObjectRequest);
            ossClient.shutdown();
            return getFileUrl(path);
        } catch (Exception e) {
            log.error("阿里云OSS上传文件失败", e);
            throw new RuntimeException("文件上传失败", e);
        }
    }

    @Override
    public boolean deleteFile(String path) {
        try {
            OSS ossClient = createOssClient();
            ossClient.deleteObject(config.getAliyunOss().getBucketName(), path);
            ossClient.shutdown();
            return true;
        } catch (Exception e) {
            log.error("阿里云OSS删除文件失败", e);
            return false;
        }
    }

    @Override
    public String getFileUrl(String path) {
        CloudStorageConfig.AliyunOssConfig ossConfig = config.getAliyunOss();
        if (ossConfig.getDomain() != null && !ossConfig.getDomain().isEmpty()) {
            return ossConfig.getDomain() + "/" + path;
        }
        return "https://" + ossConfig.getBucketName() + "." + ossConfig.getEndpoint() + "/" + path;
    }

    @Override
    public String generateThumbnail(String originalPath, String thumbnailPath, int width, int height) {
        try {
            // 从OSS下载原图
            OSS ossClient = createOssClient();
            InputStream inputStream = ossClient.getObject(
                config.getAliyunOss().getBucketName(),
                originalPath
            ).getObjectContent();

            // 生成缩略图
            BufferedImage originalImage = ImageIO.read(inputStream);
            BufferedImage thumbnailImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Graphics2D g = thumbnailImage.createGraphics();
            g.drawImage(originalImage, 0, 0, width, height, null);
            g.dispose();

            // 上传缩略图
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(thumbnailImage, "JPEG", baos);
            byte[] thumbnailData = baos.toByteArray();

            ossClient.shutdown();
            return uploadFile(thumbnailData, "thumbnail.jpg", thumbnailPath, "image/jpeg");
        } catch (Exception e) {
            log.error("生成缩略图失败", e);
            throw new RuntimeException("生成缩略图失败", e);
        }
    }

    private OSS createOssClient() {
        CloudStorageConfig.AliyunOssConfig ossConfig = config.getAliyunOss();
        return new OSSClientBuilder().build(
            ossConfig.getEndpoint(),
            ossConfig.getAccessKeyId(),
            ossConfig.getAccessKeySecret()
        );
    }
}
