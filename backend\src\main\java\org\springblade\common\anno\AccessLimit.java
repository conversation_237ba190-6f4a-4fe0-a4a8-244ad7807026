package org.springblade.common.anno;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)

public @interface AccessLimit {

    int seconds() default 60; // 时间窗口（秒）
    int maxCount() default -1; // 最大访问次数
    String key() default ""; // 限流key前缀（可选）
    String limitConfigKey() default "request.limit.count"; // 动态配置key（可选）
}
