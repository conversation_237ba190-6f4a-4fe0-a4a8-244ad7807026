/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.miniapp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.business.config.entity.MiniappRegionConfig;
import org.springblade.business.config.mapper.MiniappRegionConfigMapper;
import org.springblade.miniapp.service.WeChatRegionService;
import org.springblade.miniapp.vo.MiniappRegionVO;
import org.springblade.modules.system.entity.Region;
import org.springblade.modules.system.service.IRegionService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static org.springblade.common.cache.CacheNames.WECHAT_CATEGORY_FORMS;
import static org.springblade.common.cache.CacheNames.WECHAT_REGIN_SELECT;

/**
 * 小程序地区选择服务实现类
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class WeChatRegionServiceImpl extends ServiceImpl<MiniappRegionConfigMapper, MiniappRegionConfig> implements WeChatRegionService {

	private final IRegionService regionService;

	@Override
	@Cacheable(cacheNames = WECHAT_REGIN_SELECT)
	public List<MiniappRegionVO> getOpenRegionsByLevel(Integer level) {
		// 查询开放的地区配置
		LambdaQueryWrapper<MiniappRegionConfig> wrapper = Wrappers.<MiniappRegionConfig>lambdaQuery()
			.eq(MiniappRegionConfig::getLevel, level)
			.eq(MiniappRegionConfig::getIsOpen, 1)
			.orderByAsc(MiniappRegionConfig::getSort);

		List<MiniappRegionConfig> configs = this.list(wrapper);

		// 获取对应的地区详细信息
		List<String> regionCodes = configs.stream()
			.map(MiniappRegionConfig::getRegionCode)
			.collect(Collectors.toList());

		if (regionCodes.isEmpty()) {
			return new ArrayList<>();
		}

		List<Region> regions = regionService.list(
			Wrappers.<Region>lambdaQuery()
				.in(Region::getCode, regionCodes)
				.orderByAsc(Region::getSort)
		);

		return regions.stream()
			.map(this::convertToVO)
			.collect(Collectors.toList());
	}

	@Override
	public List<MiniappRegionVO> getOpenRegionsByParentCode(String parentCode, Integer level) {
		// 先从地区表中查询子级地区
		List<Region> childRegions = regionService.list(
			Wrappers.<Region>lambdaQuery()
				.eq(Region::getParentCode, parentCode)
				.eq(Region::getLevel, level)
				.orderByAsc(Region::getSort)
		);

		if (childRegions.isEmpty()) {
			return new ArrayList<>();
		}

		// 查询哪些地区是开放的
		List<String> regionCodes = childRegions.stream()
			.map(Region::getCode)
			.collect(Collectors.toList());

		List<MiniappRegionConfig> openConfigs = this.list(
			Wrappers.<MiniappRegionConfig>lambdaQuery()
				.in(MiniappRegionConfig::getRegionCode, regionCodes)
				.eq(MiniappRegionConfig::getIsOpen, 1)
		);

		List<String> openRegionCodes = openConfigs.stream()
			.map(MiniappRegionConfig::getRegionCode)
			.toList();

		// 返回开放的地区
		return childRegions.stream()
			.filter(region -> openRegionCodes.contains(region.getCode()))
			.map(this::convertToVO)
			.collect(Collectors.toList());
	}

	@Override
	public MiniappRegionVO getFullAddressByCode(String regionCode) {
		Region region = regionService.getOne(
			Wrappers.<Region>lambdaQuery()
				.eq(Region::getCode, regionCode)
		);
		if (region == null) {
			return null;
		}

		MiniappRegionVO vo = convertToVO(region);

		// 构建完整地址
		StringBuilder fullAddress = new StringBuilder();
		if (Func.isNotEmpty(region.getProvinceName())) {
			fullAddress.append(region.getProvinceName());
		}
		if (Func.isNotEmpty(region.getCityName())) {
			fullAddress.append(region.getCityName());
		}
		if (Func.isNotEmpty(region.getDistrictName())) {
			fullAddress.append(region.getDistrictName());
		}
		if (Func.isNotEmpty(region.getTownName())) {
			fullAddress.append(region.getTownName());
		}
		if (Func.isNotEmpty(region.getVillageName())) {
			fullAddress.append(region.getVillageName());
		}

		vo.setFullAddress(fullAddress.toString());
		return vo;
	}

	@Override
	public List<MiniappRegionVO> searchOpenRegions(String keyword) {
		// 先从地区表中搜索
		List<Region> regions = regionService.list(
			Wrappers.<Region>lambdaQuery()
				.like(Region::getName, keyword)
				.orderByAsc(Region::getLevel, Region::getSort)
		);

		if (regions.isEmpty()) {
			return new ArrayList<>();
		}

		// 查询哪些地区是开放的
		List<String> regionCodes = regions.stream()
			.map(Region::getCode)
			.collect(Collectors.toList());

		List<MiniappRegionConfig> openConfigs = this.list(
			Wrappers.<MiniappRegionConfig>lambdaQuery()
				.in(MiniappRegionConfig::getRegionCode, regionCodes)
				.eq(MiniappRegionConfig::getIsOpen, 1)
		);

		List<String> openRegionCodes = openConfigs.stream()
			.map(MiniappRegionConfig::getRegionCode)
			.toList();

		// 返回开放的地区
		return regions.stream()
			.filter(region -> openRegionCodes.contains(region.getCode()))
			.map(this::convertToVO)
			.collect(Collectors.toList());
	}

	@Override
	public MiniappRegionVO getRegionByLocation(Double latitude, Double longitude) {
		// 这里可以集成第三方地理编码服务，如腾讯地图、百度地图等
		// 暂时返回null，需要根据实际需求实现
		return null;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	@Caching(evict = {
		@CacheEvict(cacheNames = WECHAT_REGIN_SELECT,allEntries = true)
	})
	public boolean configRegionOpen(String regionCode, Integer isOpen) {
		// 查询是否已存在配置
		MiniappRegionConfig existConfig = this.getOne(
			Wrappers.<MiniappRegionConfig>lambdaQuery()
				.eq(MiniappRegionConfig::getRegionCode, regionCode)
		);

		if (existConfig != null) {
			// 更新配置
			existConfig.setIsOpen(isOpen);
			return this.updateById(existConfig);
		} else {
			// 创建新配置
			Region region = regionService.getOne(
				Wrappers.<Region>lambdaQuery()
					.eq(Region::getCode, regionCode)
			);
			if (region == null) {
				return false;
			}

			MiniappRegionConfig config = new MiniappRegionConfig();
			config.setRegionCode(regionCode);
			config.setRegionName(region.getName());
			config.setParentCode(region.getParentCode());
			config.setLevel(region.getLevel());
			config.setIsOpen(isOpen);
			config.setSort(region.getSort());

			return this.save(config);
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	@Caching(evict = {
		@CacheEvict(cacheNames = WECHAT_REGIN_SELECT,allEntries = true)
	})
	public boolean batchConfigRegionOpen(List<String> regionCodes, Integer isOpen) {
		for (String regionCode : regionCodes) {
			if (!configRegionOpen(regionCode, isOpen)) {
				return false;
			}
		}
		return true;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean syncRegionData() {
		// 获取所有地区数据
		List<Region> allRegions = regionService.list();

		for (Region region : allRegions) {
			// 检查是否已存在配置
			MiniappRegionConfig existConfig = this.getOne(
				Wrappers.<MiniappRegionConfig>lambdaQuery()
					.eq(MiniappRegionConfig::getRegionCode, region.getCode())
			);

			if (existConfig == null) {
				// 创建新配置，默认关闭
				MiniappRegionConfig config = new MiniappRegionConfig();
				config.setRegionCode(region.getCode());
				config.setRegionName(region.getName());
				config.setParentCode(region.getParentCode());
				config.setLevel(region.getLevel());
				config.setIsOpen(0); // 默认关闭
				config.setSort(region.getSort());

				this.save(config);
			}
		}

		return true;
	}

	/**
	 * 转换为VO对象
	 */
	private MiniappRegionVO convertToVO(Region region) {
		MiniappRegionVO vo = BeanUtil.copyProperties(region, MiniappRegionVO.class);
		vo.setRegionCode(region.getCode());
		vo.setRegionName(region.getName());

		// 检查是否有子级
		long childCount = regionService.count(
			Wrappers.<Region>lambdaQuery()
				.eq(Region::getParentCode, region.getCode())
		);
		vo.setHasChildren(childCount > 0);

		return vo;
	}

}
