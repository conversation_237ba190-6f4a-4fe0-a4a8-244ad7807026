package org.springblade.fileShare.controller;

import org.springblade.core.tool.api.R;
import org.springblade.fileShare.dto.FileShareDTO;
import org.springblade.fileShare.service.IFileShareService;
import org.springblade.fileShare.vo.FileShareVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;

/**
 * 文件分享控制器
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Tag(name = "文件分享", description = "文件分享相关接口")
@RestController
@RequestMapping("/blade-fileshare")
@RequiredArgsConstructor
@Validated
public class FileShareController {

    private final IFileShareService fileShareService;

    /**
     * 上传文件并生成分享密钥
     */
    @Operation(summary = "上传文件", description = "上传文件并生成6位分享密钥，有效期1天")
    @PostMapping("/upload")
    public R<FileShareVO> uploadFile(
            @Parameter(description = "上传的文件") @RequestParam("file") MultipartFile file) {
        try {
            FileShareVO result = fileShareService.uploadFile(file);
            return R.data(result);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分享文本内容并生成密钥
     */
    @Operation(summary = "分享文本", description = "分享文本内容并生成6位分享密钥，有效期1天")
    @PostMapping("/share-text")
    public R<FileShareVO> shareText(
            @Parameter(description = "文本分享信息") @Valid @RequestBody FileShareDTO dto) {
        try {
            FileShareVO result = fileShareService.shareText(dto);
            return R.data(result);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过密钥获取分享信息
     */
    @Operation(summary = "获取分享信息", description = "通过6位分享密钥获取分享信息")
    @GetMapping("/info/{shareKey}")
    public R<FileShareVO> getShareInfo(
            @Parameter(description = "分享密钥") @PathVariable @NotBlank(message = "分享密钥不能为空") String shareKey) {
        try {
            FileShareVO result = fileShareService.getByShareKey(shareKey);
            return R.data(result);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过密钥下载文件
     */
    @Operation(summary = "下载文件", description = "通过6位分享密钥下载文件")
    @GetMapping("/download/{shareKey}")
    public void downloadFile(
            @Parameter(description = "分享密钥") @PathVariable @NotBlank(message = "分享密钥不能为空") String shareKey,
            HttpServletResponse response) {
        try {
            fileShareService.downloadByShareKey(shareKey, response);
        } catch (Exception e) {
            try {
                response.setContentType("text/plain;charset=UTF-8");
                response.getWriter().write("下载失败: " + e.getMessage());
            } catch (Exception ex) {
                // 忽略异常
            }
        }
    }

    /**
     * 通过密钥获取文本内容
     */
    @Operation(summary = "获取文本内容", description = "通过6位分享密钥获取文本内容")
    @GetMapping("/text/{shareKey}")
    public R<String> getTextContent(
            @Parameter(description = "分享密钥") @PathVariable @NotBlank(message = "分享密钥不能为空") String shareKey) {
        try {
            String result = fileShareService.getTextByShareKey(shareKey);
            return R.data(result);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 生成分享密钥（测试用）
     */
    @Operation(summary = "生成分享密钥", description = "生成6位随机分享密钥（测试用）")
    @GetMapping("/generate-key")
    public R<String> generateShareKey() {
        try {
            String result = fileShareService.generateShareKey();
            return R.data(result);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
