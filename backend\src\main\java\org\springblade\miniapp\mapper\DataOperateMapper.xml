<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.springblade.miniapp.mapper.DataOperateMapper">

    <!-- 点赞相关操作 -->
    <insert id="insertLike">
        INSERT INTO urb_like(relevancy_id, user_id, type, like_time)
        VALUES(#{id}, #{userId}, #{type}, NOW())
    </insert>

    <delete id="deleteLike">
        DELETE FROM urb_like
        WHERE relevancy_id = #{id} AND user_id = #{userId} AND type = #{type}
    </delete>

    <select id="checkLikeExists" resultType="int">
        SELECT COUNT(1) FROM urb_like
        WHERE relevancy_id = #{id} AND user_id = #{userId} AND type = #{type}
    </select>

    <!-- 收藏相关操作 -->
    <insert id="insertFavorite">
        INSERT INTO urb_favorite(relevancy_id, user_id, type, fav_time)
        VALUES(#{id}, #{userId}, #{type}, NOW())
    </insert>

    <delete id="deleteFavorite">
        DELETE FROM urb_favorite
        WHERE relevancy_id = #{id} AND user_id = #{userId} AND type = #{type}
    </delete>

    <select id="checkFavoriteExists" resultType="int">
        SELECT COUNT(1) FROM urb_favorite
        WHERE relevancy_id = #{id} AND user_id = #{userId} AND type = #{type}
    </select>

    <!-- 浏览记录 -->
    <insert id="insertView">
        INSERT INTO urb_view_log(relevancy_id, user_id, type, ip, view_time)
        VALUES(#{id}, #{userId}, #{type}, #{ip}, NOW())
    </insert>

    <!-- 分享记录 -->
    <insert id="insertShare">
        INSERT INTO urb_share(relevancy_id, user_id, type, ip, share_time)
        VALUES(#{id}, #{userId}, #{type}, #{ip}, NOW())
    </insert>


</mapper>
