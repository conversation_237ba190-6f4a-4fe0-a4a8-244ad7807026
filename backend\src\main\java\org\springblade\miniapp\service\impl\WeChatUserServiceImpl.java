package org.springblade.miniapp.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springblade.business.post.service.ISupPostService;
import org.springblade.business.post.vo.FeedbackVO;
import org.springblade.business.post.vo.SupPostVO;
import org.springblade.business.user.entity.Contact;
import org.springblade.business.user.entity.WeUser;
import org.springblade.business.user.mapper.ContactMapper;
import org.springblade.business.user.mapper.FeedbackMapper;
import org.springblade.business.user.mapper.WeUserMapper;
import org.springblade.business.user.service.IContactService;
import org.springblade.business.user.service.IWeUserService;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.miniapp.service.WeChatUserService;
import org.springblade.miniapp.vo.UserProfile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.Map;

@Slf4j
@Service
public class WeChatUserServiceImpl implements WeChatUserService {

	@Resource
	private IWeUserService userService;

	@Resource
	private IContactService contactService;

	@Resource
	private ContactMapper contactMapper;

	@Resource
	private WeUserMapper weUserMapper;

	@Resource
	private ISupPostService postService;

	@Resource
	private FeedbackMapper feedbackMapper;

	@Override
	public UserProfile getUserInfo(Long id) {
		WeUser user = userService.getById(id);
		UserProfile profile = BeanUtil.copyProperties(user, UserProfile.class);
//		Contact existingContact = contactMapper.getUserContact(id);
//		Contact contact = Optional.ofNullable(existingContact).orElse(new Contact());
//		BeanUtil.copyProperties(contact, profile, "id");
		//查询用户的帖子总数
		Long postCount = postService.getPostCountByUserId(id);
		profile.setPostCount(postCount);
		//查询用户的反馈总数
		Long feedbackCount = feedbackMapper.getFeedbackCountByUserId(id);
		profile.setFeedbackCount(feedbackCount);
		return profile;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean updateUserInfo(UserProfile userProfile) {
		Long userId = AuthUtil.getUserId();

		// 更新用户基本信息
		WeUser weUser = BeanUtil.copyProperties(userProfile, WeUser.class);
		weUser.setId(userId);
		boolean userUpdateResult = userService.updateById(weUser);

		// 更新联系方式信息
		Contact contact = BeanUtil.copyProperties(userProfile, Contact.class);
		contact.setTenantId(AuthUtil.getTenantId());

		// 获取现有联系方式
		Contact existingContact = contactMapper.getUserContact(userId);
		if (existingContact != null) {
			contact.setId(existingContact.getId());
			contactService.updateById(contact);
		} else {
			contactService.save(contact);
			// 同时绑定关联
			contactMapper.insertUserContact(userId, contact.getId());
		}

		return userUpdateResult;
	}

	@Override
	public Map<String, Object> getUserStats() {
		Long userId = AuthUtil.getUserId();
		Map<String, Object> userStats = weUserMapper.getUserStats(userId);

		//获取本周的日期
		LocalDate start = LocalDate.now().with(DayOfWeek.MONDAY);
		LocalDate end = LocalDate.now().with(DayOfWeek.SUNDAY);
		//获取本周的发帖数量
		Long postCount = postService.getPostCountByUserIdAndDate(userId, start, end);
		userStats.put("weekPostCount", postCount);
		//获取用户的积分数量
		userStats.put("points",getPoints(userId));
		return userStats;
	}

	/**
	 * 获取用户帖子列表
	 * @param userId
	 * @param query
	 * @return
	 */
	@Override
	public IPage<SupPostVO> getPostListByUserId(Long userId, Query query) {
		return postService.getPostListByUserId(userId, query);
	}

	/**
	 * 获取用户反馈列表
	 * @param userId
	 * @param query
	 * @return
	 */
	@Override
	public IPage<FeedbackVO> getFeedbackList(Long userId, Query query) {
		Page<FeedbackVO> page = new Page<>(query.getCurrent(), query.getSize());
		return feedbackMapper.selectFeedbackPageByUserId(page, userId);
	}

	@Override
	public Integer getPoints(Long userId) {
		WeUser user =userService.getById(userId);
		return user.getBalance();
	}

}
