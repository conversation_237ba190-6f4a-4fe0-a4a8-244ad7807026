package org.springblade.miniapp.service;

import org.springblade.business.config.service.IUrbMenuService;
import org.springblade.business.config.vo.UrbMenuVO;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import lombok.AllArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.springblade.common.cache.CacheNames.DICT_VALUE;
import static org.springblade.common.cache.CacheNames.WECHAT_MENU_LIST;

@Service
@AllArgsConstructor
public class WeChatMenuService {
    private final IUrbMenuService urbMenuService;

	@Cacheable(cacheNames = WECHAT_MENU_LIST)
    public Map<String, List<UrbMenuVO>> getMenusAndBanners() {
        UrbMenuVO menuQuery = new UrbMenuVO();
        menuQuery.setCategory(0);
        List<UrbMenuVO> menus = urbMenuService.selectUrbMenuPage(null, menuQuery).getRecords();

        UrbMenuVO bannerQuery = new UrbMenuVO();
        bannerQuery.setCategory(1);
        List<UrbMenuVO> banners = urbMenuService.selectUrbMenuPage(null, bannerQuery).getRecords();

		UrbMenuVO userQuery = new UrbMenuVO();
		userQuery.setCategory(2);
		List<UrbMenuVO> userMenus = urbMenuService.selectUrbMenuPage(null,userQuery).getRecords();

        Map<String, List<UrbMenuVO>> result = new HashMap<>();
        result.put("menus", menus);
        result.put("banners", banners);
		result.put("userMenus",userMenus);
        return result;
    }
}
