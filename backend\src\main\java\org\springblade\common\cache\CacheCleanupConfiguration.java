package org.springblade.common.cache;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springblade.business.user.service.RedisRequestStatsService;
import org.springblade.common.aspect.RecordRequestAspect;
import org.springblade.core.schedule.RequestStatsSyncService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Set;

import static org.springblade.common.cache.CacheNames.*;

/**
 * 缓存清理配置
 * 在应用启动时清理可能有问题的缓存
 *
 * <AUTHOR>
 */
@Slf4j
//@Component
public class CacheCleanupConfiguration implements CommandLineRunner {
	@Resource
	private RequestStatsSyncService requestStatsSyncService;
	@Resource
	private RedisRequestStatsService redisService;
	@Override
	public void run(String... args) throws Exception {
		redisService.recordRequest("2111");
		redisService.recordRequest("2111");
		redisService.recordRequest("2111");
		requestStatsSyncService.syncRequestStatsToMySQL();
	}

}
