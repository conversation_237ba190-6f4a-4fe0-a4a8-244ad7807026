package org.springblade.fileShare.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 文件分享DTO
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Schema(description = "文件分享DTO")
public class FileShareDTO {

    /**
     * 分享类型（FILE-文件，TEXT-文本）
     */
    @Schema(description = "分享类型", required = true)
    @NotBlank(message = "分享类型不能为空")
    private String shareType;

    /**
     * 文件名称（文件分享时必填）
     */
    @Schema(description = "文件名称")
    private String fileName;

    /**
     * 文件路径（文件分享时必填）
     */
    @Schema(description = "文件路径")
    private String filePath;

    /**
     * 文件大小（字节）
     */
    @Schema(description = "文件大小")
    private Long fileSize;

    /**
     * 文件类型
     */
    @Schema(description = "文件类型")
    private String fileType;

    /**
     * 文本内容（文本分享时必填，不超过1024字符）
     */
    @Schema(description = "文本内容")
    @Size(max = 1024, message = "文本内容不能超过1024个字符")
    private String textContent;
}
