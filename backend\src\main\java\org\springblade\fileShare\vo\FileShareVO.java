package org.springblade.fileShare.vo;

import java.time.LocalDateTime;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 文件分享VO
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Schema(description = "文件分享VO")
public class FileShareVO {

    /**
     * 分享密钥（6位字符）
     */
    @Schema(description = "分享密钥")
    private String shareKey;

    /**
     * 分享类型（FILE-文件，TEXT-文本）
     */
    @Schema(description = "分享类型")
    private String shareType;

    /**
     * 文件名称
     */
    @Schema(description = "文件名称")
    private String fileName;

    /**
     * 文件大小（字节）
     */
    @Schema(description = "文件大小")
    private Long fileSize;

    /**
     * 文件类型
     */
    @Schema(description = "文件类型")
    private String fileType;

    /**
     * 文本内容
     */
    @Schema(description = "文本内容")
    private String textContent;

    /**
     * 过期时间
     */
    @Schema(description = "过期时间")
    private LocalDateTime expireTime;

    /**
     * 下载次数
     */
    @Schema(description = "下载次数")
    private Integer downloadCount;

    /**
     * 状态（0-正常，1-已过期，2-已删除）
     */
    @Schema(description = "状态")
    private Integer status;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}
