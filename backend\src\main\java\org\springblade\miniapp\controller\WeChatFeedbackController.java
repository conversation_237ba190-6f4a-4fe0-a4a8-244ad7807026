package org.springblade.miniapp.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import jakarta.validation.Valid;
import org.springblade.business.painpoint.entity.PainPoint;
import org.springblade.business.post.dto.CommentDTO;
import org.springblade.business.post.entity.Feedback;
import org.springblade.business.post.service.IFeedbackService;
import org.springblade.business.report.entity.Report;
import org.springblade.business.post.vo.FeedbackVO;
import org.springblade.common.anno.AccessLimit;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.miniapp.service.WeChatFeedbackService;
import org.springblade.miniapp.service.WeChatReportService;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;

import io.swagger.v3.oas.annotations.Operation;

import lombok.AllArgsConstructor;

import java.util.List;
import java.util.Map;

@RestController
@AllArgsConstructor
@RequestMapping("/blade-chat/feedback")
@io.swagger.v3.oas.annotations.tags.Tag(name = "小程序反馈记录接口")
public class WeChatFeedbackController {

	private final WeChatFeedbackService feedbackService;
	private final IFeedbackService iFeedbackService;
	private final WeChatReportService weChatReportService;

	/**
	 * 分页查询  传入反馈类型
	 * 机构/帖子
	 */
	@GetMapping("/page")
	@Operation(summary = "分页查询")
	public R<IPage<FeedbackVO>> page(FeedbackVO feedback, Query query) {
		IPage<FeedbackVO> pages = feedbackService.page(Condition.getPage(query), feedback);
		return R.data(pages);
	}

	/**
	 * 提交反馈 机构/帖子
	 */
	@PostMapping("/submit")
	@Operation(summary = "提交反馈")
	public R<Long> submit(@RequestBody Feedback feedback) {
		return R.data(feedbackService.submit(feedback));
	}

	/**
	 * 删除反馈
	 */
	@DeleteMapping("/remove")
	@Operation(summary = "删除反馈")
	public R<Boolean> removeMore(@RequestParam String ids) {
		return R.status(feedbackService.remove(ids));
	}
	/**
	 * 删除反馈
	 */
	@DeleteMapping("/remove/{id}")
	@Operation(summary = "删除反馈")
	public R<Boolean> remove(@PathVariable String id) {
		return R.status(feedbackService.remove(id));
	}

	@Operation(summary = "标记反馈有帮助")
	@PostMapping("/helpful/{id}")
	public R<Boolean> toggleHelpful(@PathVariable Long id, @RequestParam(defaultValue = "LIKE") String likeType) {
		return R.data(feedbackService.toggleCommentLike(id, likeType));
	}



	/**
	 * 新增 举报记录
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入report")
	public R<Long> save(@Valid @RequestBody Report report) {
		return R.data(weChatReportService.saveReport(report));
	}

	/**
	 * 新增或修改 举报记录
	 */
	@PostMapping("/report/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description = "传入report")
	public R<Long> submit(@Valid @RequestBody Report report) {
		return R.data(weChatReportService.saveOrUpdateReport(report));
	}


	@PostMapping("/painpoint/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入painPoint")
	public R save(@Valid @RequestBody PainPoint painPoint) {
		return R.status(feedbackService.save(painPoint));
	}


	//根据分类Id查询反馈标签
	@GetMapping("/getTagsByCategory")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "根据分类Id查询反馈标签", description = "传入categoryId")
	public R<List<Map<String, Object>>> getTagsByCategory(Long categoryId) {
		return R.data(iFeedbackService.getTagsByCategory(categoryId));
	}

	/**
	 * 添加评论
	 */
	@PostMapping("/comment/add")
	@AccessLimit(seconds = 10)
	@Operation(summary = "添加评论")
	public R<Long> addComment(@Valid @RequestBody CommentDTO commentDTO) {
		return R.data(feedbackService.addComment(commentDTO));
	}

	/**
	 * 回复评论
	 */
	@PostMapping("/comment/reply")
	@AccessLimit(seconds = 10)
	@Operation(summary = "回复评论")
	public R<Long> replyComment(@Valid @RequestBody CommentDTO commentDTO) {
		return R.data(feedbackService.replyComment(commentDTO));
	}

	/**
	 * 获取评论列表（包含回复）
	 */
	@GetMapping("/comment/list")
	@Operation(summary = "获取评论列表")
	public R<IPage<FeedbackVO>> getCommentList(
		@RequestParam Long postId,
		Query query
	) {
		return R.data(feedbackService.getCommentList(postId, query));
	}

	/**
	 * 获取评论回复列表
	 */
	@GetMapping("/comment/replies/{parentId}")
	@Operation(summary = "获取评论回复列表")
	public R<List<FeedbackVO>> getCommentReplies(@PathVariable Long parentId) {
		return R.data(feedbackService.getCommentReplies(parentId));
	}

	/**
	 * 删除评论
	 */
	@DeleteMapping("/comment/remove/{id}")
	@Operation(summary = "删除评论")
	public R<Boolean> removeComment(@PathVariable Long id) {
		return R.status(feedbackService.removeComment(id));
	}

	/**
	 * 点赞/取消点赞评论
	 */
	@PostMapping("/comment/like/{id}")
	@Operation(summary = "点赞评论")
	public R<Boolean> likeComment(@PathVariable Long id, @RequestParam(defaultValue = "LIKE") String likeType) {
		return R.data(feedbackService.toggleCommentLike(id, likeType));
	}

}
