/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.post.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

import java.io.Serial;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 用户反馈实体类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Data
@TableName("urb_feedback")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "用户反馈")
public class Feedback extends BaseEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 关联ID
	 */
	@Schema(description = "关联ID")
	private Long relevancyId;

	/**
	 * 关联类型
	 */
	@Schema(description = "关联类型")
	private String type;

	/**
	 * 评论星级
	 */
	@Schema(description = "评论星级")
	private Integer star;



	/**
	 * 反馈用户ID
	 */
	@Schema(description = "反馈用户ID")
	private Long userId;
	/**
	 * 父评论ID，NULL表示顶级评论
	 */
	@Schema(description = "父评论ID")
	private Long parentId;

	/**
	 * 被回复的用户ID
	 */
	@Schema(description = "被回复的用户ID")
	private Long replyToUserId;

	/**
	 * 被回复的用户昵称
	 */
	@Schema(description = "被回复的用户昵称")
	private String replyToUserName;

	/**
	 * 评论类型：FEEDBACK-反馈，COMMENT-评论，REPLY-回复
	 */
	@Schema(description = "评论类型")
	private String commentType;

	/**
	 * 评论层级，1为顶级评论
	 */
	@Schema(description = "评论层级")
	private Integer level;

	/**
	 * 回复数量
	 */
	@Schema(description = "回复数量")
	private Integer replyCount;

	/**
	 * 反馈内容
	 */
	@Schema(description = "反馈内容")
	private String content;

	/**
	 * 评论图片URL
	 */
	@Schema(description = "评论图片URL")
	private String image;

	/**
	 * 联系方式
	 */
	@Schema(description = "联系方式")
	private String contactInfo;

	@Schema(description = "审核状态")
	private String auditStatus;

	@Schema(description = "理由")
	private String reason;

	/**
	 * 帖子分类
	 *
	 */
	@Schema(description = "帖子分类")
	@TableField(exist = false)
	private String categoryName;


	/**
	 * 审核时间
	 */
	@Schema(description = "审核时间")
	private LocalDateTime auditTime;

	/**
	 * 审核人ID
	 */
	@Schema(description = "审核人ID")
	private Long auditUserId;

	/**
	 * 石否为作者（0-否，1-是）
	 */
	@Schema(description = "是否为作者")
	public Integer IsAuthor;

	/**
	 * 是否匿名
	 */
   @Schema(description = "是否匿名")
	private Integer isAnonymous;
}
