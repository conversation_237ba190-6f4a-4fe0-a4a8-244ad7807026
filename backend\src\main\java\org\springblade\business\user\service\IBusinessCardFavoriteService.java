package org.springblade.business.user.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.business.user.entity.BusinessCardFavorite;
import org.springblade.business.user.vo.BusinessCardFavoriteVO;
import org.springblade.business.user.vo.BusinessCardVO;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.mp.support.Query;

/**
 * 名片收藏表 服务类
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
public interface IBusinessCardFavoriteService extends BaseService<BusinessCardFavorite> {

    /**
     * 分页查询公开名片列表
     *
     * @param query 分页参数
     * @param businessCard 查询条件
     * @return 名片列表
     */
    IPage<BusinessCardVO> getPublicCardList(Query query, BusinessCardVO businessCard);

    /**
     * 分页查询我的收藏名片列表
     *
     * @param query 分页参数
     * @param businessCard 查询条件
     * @return 收藏名片列表
     */
    IPage<BusinessCardFavoriteVO> getMyFavoriteCardList(Query query, BusinessCardVO businessCard);

    /**
     * 收藏名片
     *
     * @param cardId 名片ID
     * @param category 收藏分类
     * @param remark 收藏备注
     * @return 操作结果
     */
    Boolean favoriteCard(Long cardId, String category, String remark);

    /**
     * 取消收藏名片
     *
     * @param cardId 名片ID
     * @return 操作结果
     */
    Boolean unfavoriteCard(Long cardId);

    /**
     * 切换名片点赞状态
     *
     * @param cardId 名片ID
     * @return 操作结果
     */
    Boolean toggleCardLike(Long cardId);

    /**
     * 检查用户是否已收藏名片
     *
     * @param cardId 名片ID
     * @return 是否已收藏
     */
    Boolean isCardFavorited(Long cardId);

    /**
     * 检查用户是否已点赞名片
     *
     * @param cardId 名片ID
     * @return 是否已点赞
     */
    Boolean isCardLiked(Long cardId);
}
