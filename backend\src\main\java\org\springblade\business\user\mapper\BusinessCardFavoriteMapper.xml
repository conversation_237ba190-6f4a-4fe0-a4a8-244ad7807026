<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.user.mapper.BusinessCardFavoriteMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="businessCardFavoriteResultMap" type="org.springblade.business.user.entity.BusinessCardFavorite">
        <id column="id" property="id"/>
        <result column="card_id" property="cardId"/>
        <result column="user_id" property="userId"/>
        <result column="card_snapshot" property="cardSnapshot"/>
        <result column="category" property="category"/>
        <result column="remark" property="remark"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 公开名片查询结果映射 -->
    <resultMap id="publicCardResultMap" type="org.springblade.business.user.vo.BusinessCardVO">
        <id column="id" property="id"/>
        <result column="company" property="company"/>
        <result column="job_title" property="jobTitle"/>
        <result column="business_profile" property="businessProfile"/>
        <result column="full_name" property="fullName"/>
        <result column="gender" property="gender"/>
        <result column="phone" property="phone"/>
        <result column="address" property="address"/>
        <result column="email" property="email"/>
        <result column="website" property="website"/>
        <result column="weixin" property="weixin"/>
        <result column="avatar" property="avatar"/>
        <result column="images" property="images"/>
        <result column="video" property="video"/>
        <result column="description" property="description"/>
        <result column="is_public" property="isPublic"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="view_count" property="viewCount"/>
        <result column="like_count" property="likeCount"/>
        <result column="favorite_count" property="favoriteCount"/>
        <result column="is_favorited" property="isFavorited"/>
        <result column="is_liked" property="isLiked"/>
        <result column="distance" property="distance"/>
    </resultMap>

    <!-- 收藏名片查询结果映射 -->
    <resultMap id="favoriteCardResultMap" type="org.springblade.business.user.vo.BusinessCardFavoriteVO">
        <id column="id" property="id"/>
        <result column="original_card_id" property="originalCardId"/>
        <result column="user_id" property="userId"/>
        <result column="card_snapshot" property="cardSnapshot"/>
        <result column="category" property="category"/>
        <result column="remark" property="remark"/>
        <result column="favorite_time" property="favoriteTime"/>
        <result column="is_liked" property="isLiked"/>
        <result column="like_time" property="likeTime"/>
        <result column="create_time" property="createTime"/>
        <result column="user_nickname" property="userNickname"/>
        <result column="user_avatar" property="userAvatar"/>
        <result column="like_count" property="likeCount"/>
        <result column="favorite_count" property="favoriteCount"/>
    </resultMap>

    <!-- 分页查询公开名片列表 -->
    <select id="selectPublicCardList" resultMap="publicCardResultMap">
        SELECT
            bc.*,
            COALESCE(fav_stats.view_count, 0) as view_count,
            COALESCE(like_stats.like_count, 0) as like_count,
            COALESCE(fav_stats.favorite_count, 0) as favorite_count,
            CASE WHEN fav.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited,
            CASE WHEN like_log.id IS NOT NULL THEN 1 ELSE 0 END as is_liked,
            <if test="params.latitude != null and params.longitude != null">
                ROUND(
                    6371 * acos(
                        cos(radians(#{params.latitude})) * cos(radians(bc.latitude)) *
                        cos(radians(bc.longitude) - radians(#{params.longitude})) +
                        sin(radians(#{params.latitude})) * sin(radians(bc.latitude))
                    ), 2
                ) as distance
            </if>
            <if test="params.latitude == null or params.longitude == null">
                NULL as distance
            </if>
        FROM urb_business_card bc
        LEFT JOIN (
            SELECT
                relevancy_id,
                SUM(CASE WHEN type = '2' THEN 1 ELSE 0 END) as view_count,
                0 as like_count,
                0 as favorite_count
            FROM urb_favorite
            WHERE is_deleted = 0 AND type = '2'
            GROUP BY relevancy_id
        ) fav_stats ON bc.id = fav_stats.relevancy_id
        LEFT JOIN (
            SELECT
                relevancy_id,
                COUNT(*) as like_count
            FROM urb_like
            WHERE is_deleted = 0 AND type = '2'
            GROUP BY relevancy_id
        ) like_stats ON bc.id = like_stats.relevancy_id
        LEFT JOIN urb_favorite fav ON bc.id = fav.relevancy_id
            AND fav.user_id = #{params.currentUserId} AND fav.is_deleted = 0 AND fav.type = '2'
        LEFT JOIN urb_like like_log ON bc.id = like_log.relevancy_id
            AND like_log.user_id = #{params.currentUserId} AND like_log.is_deleted = 0 AND like_log.type = '2'
        WHERE bc.is_deleted = 0
            AND bc.is_public = 1
            AND bc.audit_status = 1
            AND bc.create_user != #{params.currentUserId}
            <if test="params.company != null and params.company != ''">
                AND bc.company LIKE CONCAT('%', #{params.company}, '%')
            </if>
            <if test="params.jobTitle != null and params.jobTitle != ''">
                AND bc.job_title LIKE CONCAT('%', #{params.jobTitle}, '%')
            </if>
            <if test="params.fullName != null and params.fullName != ''">
                AND bc.full_name LIKE CONCAT('%', #{params.fullName}, '%')
            </if>
            <if test="params.address != null and params.address != ''">
                AND bc.address LIKE CONCAT('%', #{params.address}, '%')
            </if>
            <if test="params.scope != null and params.latitude != null and params.longitude != null">
                AND (
                    6371 * acos(
                        cos(radians(#{params.latitude})) * cos(radians(bc.latitude)) *
                        cos(radians(bc.longitude) - radians(#{params.longitude})) +
                        sin(radians(#{params.latitude})) * sin(radians(bc.latitude))
                    )
                ) &lt;= #{params.scope}
            </if>
        ORDER BY
            <if test="params.latitude != null and params.longitude != null">
                distance ASC,
            </if>
            bc.create_time DESC
    </select>

    <!-- 分页查询我的收藏名片列表 -->
    <select id="selectMyFavoriteCardList" resultMap="favoriteCardResultMap">
        SELECT
            bcf.*,
            u.nickname as user_nickname,
            u.avatar as user_avatar,
            COALESCE(like_stats.like_count, 0) as like_count,
            COALESCE(fav_stats.favorite_count, 0) as favorite_count
        FROM urb_business_card_favorite_ext bcf
        LEFT JOIN urb_user u ON bcf.create_user = u.id
        LEFT JOIN (
            SELECT
                relevancy_id,
                COUNT(*) as like_count
            FROM urb_like
            WHERE is_deleted = 0 AND type = '2'
            GROUP BY relevancy_id
        ) like_stats ON bcf.card_id = like_stats.relevancy_id
        LEFT JOIN (
            SELECT
                relevancy_id,
                COUNT(*) as favorite_count
            FROM urb_favorite
            WHERE is_deleted = 0 AND type = '2'
            GROUP BY relevancy_id
        ) fav_stats ON bcf.card_id = fav_stats.relevancy_id
        WHERE bcf.is_deleted = 0
            AND bcf.user_id = #{params.userId}
            <if test="params.category != null and params.category != ''">
                AND bcf.category = #{params.category}
            </if>
            <if test="params.remark != null and params.remark != ''">
                AND bcf.remark LIKE CONCAT('%', #{params.remark}, '%')
            </if>
        ORDER BY bcf.create_time DESC
    </select>

    <!-- 删除名片收藏扩展信息 -->
    <update id="deleteCardFavoriteExt">
        UPDATE urb_business_card_favorite_ext
        SET is_deleted = 1, update_time = NOW()
        WHERE card_id = #{params.cardId}
            AND user_id = #{params.userId}
            AND is_deleted = 0
    </update>

    <!-- 获取名片统计信息 -->
    <select id="getCardStats" resultType="map">
        SELECT
            COUNT(*) as favorite_count,
            SUM(CASE WHEN is_liked = 1 THEN 1 ELSE 0 END) as like_count
        FROM urb_business_card_favorite
        WHERE original_card_id = #{cardId}
            AND is_deleted = 0
    </select>

</mapper>
