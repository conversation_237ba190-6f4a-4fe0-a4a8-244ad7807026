package org.springblade.miniapp.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springblade.business.user.entity.BusinessCard;
import org.springblade.business.user.mapper.BusinessCardMapper;
import org.springblade.business.user.service.IBusinessCardService;
import org.springblade.business.user.vo.BusinessCardVO;
import org.springblade.common.constant.bizz.AuditStatusEnum;
import org.springblade.common.constant.bizz.PublishStatusEnum;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.exception.SecureException;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.miniapp.command.BusinessCardUpdateCommand;
import org.springblade.miniapp.service.WeChatBusinessCardService;
import org.springblade.modules.system.service.IParamService;
import org.springblade.business.user.service.IBusinessCardFavoriteService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@AllArgsConstructor
@Service
public class WeChatBusinessCardServiceImpl implements WeChatBusinessCardService {

	private BusinessCardMapper baseMapper;
	private IBusinessCardService businessCardService;
	private IBusinessCardFavoriteService businessCardFavoriteService;
	private IParamService paramService;
	@Override
	public IPage<BusinessCardVO> getHomeCartList(Query query, BusinessCardVO businessCard){
		//判断是否未登录且页码大于5页
		Long userId = AuthUtil.getUserId();
		if ((userId == null || userId == -1L) && query.getCurrent() > 5) {
			throw new SecureException("登录访问更多数据");
		}
		Map<String, Object> params = new HashMap<>();
		if (getEnableAudit()) {
			params.put("auditStatus", AuditStatusEnum.APPROVED.getCode());
		}
		params.put("publishStatus", PublishStatusEnum.PUBLISHED.getValue());

		// 添加当前用户ID用于查询点赞和收藏状态
		params.put("visitUser", userId);

		if (businessCard.getLatitude() != null && businessCard.getLongitude() != null ) {
			params.put("latitude", businessCard.getLatitude());
			params.put("longitude", businessCard.getLongitude());
		}
		//如果范围大于50km就报错
		if (businessCard.getScope() != null) {
			if (businessCard.getScope() > 50){
				throw new ServiceException("范围不能大于50km");
			}else if (businessCard.getScope() < 0){
				throw new ServiceException("范围不能小于0");
			}
			params.put("scope", businessCard.getScope());
		}else {
			params.put("scope", 50);  // 默认50
		}
		Map<String, Object> mtp = BeanUtil.beanToMap(businessCard, false, true);
		params.putAll(mtp);
		params.put("status", 1);
        return baseMapper.selectCardList(Condition.getPage(query), params);
	}

	@Override
	@Transactional
	public boolean create(BusinessCardUpdateCommand businessCard) {
		BusinessCard saveEn = new BusinessCard();
		BeanUtil.copyProperties(businessCard, saveEn,"id");
		saveEn.setPublishStatus(PublishStatusEnum.PUBLISHED.getValue());
		saveEn.setAuditStatus(AuditStatusEnum.PENDING.getCode());
		saveEn.setOwnerId(AuthUtil.getUserId());
		return this.baseMapper.insert(saveEn) > 0;
	}

	@Override
	public List<BusinessCard> getCardByUserId(Long eq) {
		return businessCardService.listByUserId(AuthUtil.getUserId());
	}

	@Override
	public boolean delete(List<Long> ids) {
		return businessCardService.removeByIds(ids);
	}

	@Override
	public boolean update(@Valid BusinessCardUpdateCommand businessCard) {
		BusinessCard saveEn = new BusinessCard();
		BeanUtil.copyProperties(businessCard, saveEn);
		saveEn.setAuditStatus(AuditStatusEnum.PENDING.getCode());
		saveEn.setOwnerId(AuthUtil.getUserId());
		return businessCardService.updateById(saveEn);
	}


	@Override
	public boolean toggleCardFavorite(Long cardId) {
		// 使用BusinessCardFavoriteService处理名片收藏，包含快照保存
		return businessCardFavoriteService.favoriteCard(cardId, null, null);
	}

	@Override
	public boolean isCardFavorited(Long cardId) {
		return businessCardFavoriteService.isCardFavorited(cardId);
	}

	private boolean getEnableAudit() {
		return "true".equals(paramService.getByKey("card.preAudit.enabled"));
	}
}
