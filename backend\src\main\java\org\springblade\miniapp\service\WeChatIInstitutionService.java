/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.miniapp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import jakarta.validation.Valid;
import org.springblade.business.institution.entity.Institution;
import org.springblade.business.institution.entity.InstitutionType;
import org.springblade.business.institution.vo.InstitutionVO;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.mp.support.Query;

import java.util.List;
import java.util.Map;

/**
 * 机构主表 服务类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
public interface WeChatIInstitutionService extends BaseService<Institution> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param
	 * @return
	 */
	IPage<InstitutionVO> selectInstitutionPage(IPage<InstitutionVO> page, Map<String, Object> params);

	InstitutionVO getInstitutionDetailWithPosts(InstitutionVO institution);

	boolean applyInstitution(@Valid Institution institution);

	IPage<InstitutionVO> getInstitutionPage(InstitutionVO institution, Query query);

	boolean updateInstitutionById(@Valid Institution institution);

	boolean openOrClose(String ids);

	IPage<InstitutionVO> pageByUserId(IPage<Object> page);

	List<InstitutionType> getInstitutionTypes();

	boolean like(Long id);

	boolean favorite(Long id);
}
