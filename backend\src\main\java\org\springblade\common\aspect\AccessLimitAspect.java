package org.springblade.common.aspect;

import com.alibaba.druid.util.StringUtils;
import jakarta.servlet.http.HttpServletRequest;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springblade.common.anno.AccessLimit;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.miniapp.service.LimitConfigService;
import org.springblade.miniapp.utils.IpUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Aspect
@Component
public class AccessLimitAspect {

	@Autowired
	private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private LimitConfigService limitConfigService; // 动态配置服务

    @Around("@annotation(accessLimit)")
    public Object around(ProceedingJoinPoint pjp, AccessLimit accessLimit) throws Throwable {
        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        String ip = IpUtils.getIpAddr(request);
        String uri = request.getRequestURI();
        String key = accessLimit.key();
        if (StringUtils.isEmpty(key)) {
            key = uri + ":" + ip;
        }
        // 动态获取阈值
        int maxCount = accessLimit.maxCount();
        if (maxCount == -1 && !StringUtils.isEmpty(accessLimit.limitConfigKey())) {
			Integer dynamic = limitConfigService.getLimit(accessLimit.limitConfigKey());
            if (dynamic != null) maxCount = dynamic;
        }
        int seconds = accessLimit.seconds();

        // Redis计数
		Long count = redisTemplate.opsForValue().increment(key, 1);
		if (count != null && count == 1) {
			// 第一次自增，设置过期时间
			redisTemplate.expire(key, seconds, TimeUnit.SECONDS);
		}
		if (count != null && count > maxCount) {
			throw new ServiceException("访问过于频繁，请稍后再试");
		}
        return pjp.proceed();
    }

}
