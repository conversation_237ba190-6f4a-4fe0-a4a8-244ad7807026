package org.springblade.miniapp.auth;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaUserInfo;
import cn.binarywang.wx.miniapp.util.WxMaConfigHolder;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springblade.core.secure.utils.AuthUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 通用微信小程序用户接口
 *
 * <AUTHOR> href="https://github.com/binarywang">Binary Wang</a>
 */
@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping("/wx/user/{appid}")
public class WxMaUserController {
	private final WxMaService wxMaService;

	/**
	 * 登陆接口
	 */
	@GetMapping("/login")
	public String login(@PathVariable String appid, String code) {
		if (StrUtil.isBlank(code)) {
			return "empty jscode";
		}

		if (!wxMaService.switchover(appid)) {
			throw new IllegalArgumentException(String.format("未找到对应appid=[%s]的配置，请核实！", appid));
		}

		try {
			WxMaJscode2SessionResult session = wxMaService.getUserService().getSessionInfo(code);
			log.info(session.getSessionKey());
			log.info(session.getOpenid());
			//TODO 可以增加自己的逻辑，关联业务相关数据
			return JSONUtil.toJsonStr(session);
		} catch (WxErrorException e) {
			log.error(e.getMessage(), e);
			return e.toString();
		} finally {
			WxMaConfigHolder.remove();//清理ThreadLocal
		}
	}

	/**
	 * <pre>
	 * 获取用户信息接口
	 * </pre>
	 */
	@GetMapping("/info")
	public String info(@PathVariable String appid, String sessionKey,
					   String signature, String rawData, String encryptedData, String iv) {
		if (!wxMaService.switchover(appid)) {
			throw new IllegalArgumentException(String.format("未找到对应appid=[%s]的配置，请核实！", appid));
		}

		// 用户信息校验
		if (!wxMaService.getUserService().checkUserInfo(sessionKey, rawData, signature)) {
			WxMaConfigHolder.remove();//清理ThreadLocal
			return "user check failed";
		}

		// 解密用户信息
		WxMaUserInfo userInfo = wxMaService.getUserService().getUserInfo(sessionKey, encryptedData, iv);
		WxMaConfigHolder.remove();//清理ThreadLocal
		return JSONUtil.toJsonStr(userInfo);
	}
//
//    /**
//     * <pre>
//     * 获取用户绑定手机号信息
//     * </pre>
//     */
//    @GetMapping("/phone")
//    public String phone(@PathVariable String appid, String sessionKey, String signature,
//                        String rawData, String encryptedData, String iv) {
//        if (!wxMaService.switchover(appid)) {
//            throw new IllegalArgumentException(String.format("未找到对应appid=[%s]的配置，请核实！", appid));
//        }
//
//        // 用户信息校验
//        if (!wxMaService.getUserService().checkUserInfo(sessionKey, rawData, signature)) {
//            WxMaConfigHolder.remove();//清理ThreadLocal
//            return "user check failed";
//        }
//
//        // 解密
//        WxMaPhoneNumberInfo phoneNoInfo = wxMaService.getUserService().getPhoneNoInfo(sessionKey, encryptedData, iv);
//        WxMaConfigHolder.remove();//清理ThreadLocal
//        return JsonUtils.toJson(phoneNoInfo);
//    }

}
