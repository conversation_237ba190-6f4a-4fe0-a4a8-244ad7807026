package org.springblade.miniapp.controller.open;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springblade.business.user.entity.BusinessCard;
import org.springblade.business.user.service.IBusinessCardService;
import org.springblade.business.user.vo.BusinessCardVO;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.miniapp.service.WeChatBusinessCardService;
import org.springblade.business.user.service.IBusinessCardFavoriteService;
import org.springblade.miniapp.dto.DataOperateResultDTO;
import org.springframework.web.bind.annotation.*;

@RestController
@AllArgsConstructor
@RequestMapping("/blade-chat-open/card")
@io.swagger.v3.oas.annotations.tags.Tag(name = "小程序个人名片接口")
public class WeChatBusinessCardOpenController {

	@Resource
	private final IBusinessCardService businessCardService;

	@Resource
	private final WeChatBusinessCardService weChatBusinessCardService;

	@Resource
	private final IBusinessCardFavoriteService businessCardFavoriteService;




	/**
	 * 名片详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "详情", description = "传入businessCard")
	public R<BusinessCardVO> detail(BusinessCardVO businessCard) {
		return R.data(businessCardService.getDetail(businessCard));
	}

	/**
	 * 自定义分页 名片信息表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入businessCard")
	public R<IPage<BusinessCardVO>> page(BusinessCardVO businessCard, Query query) {
		IPage<BusinessCardVO> pages = weChatBusinessCardService.getHomeCartList(query, businessCard);
		return R.data(pages);
	}

	/**
	 * 切换名片点赞状态
	 */
	@PostMapping("/toggle-like/{cardId}")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "切换点赞状态", description = "点赞/取消点赞名片")
	public R<DataOperateResultDTO> toggleLike(
			@Parameter(description = "名片ID") @PathVariable Long cardId) {

		boolean isLiked = businessCardFavoriteService.toggleCardLike(cardId);
		DataOperateResultDTO result = DataOperateResultDTO.createLikeResult(cardId, "2", isLiked);
		return R.data(result);
	}

	/**
	 * 检查名片点赞状态
	 */
	@GetMapping("/check-like/{cardId}")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "检查点赞状态", description = "检查用户是否已点赞名片")
	public R<Boolean> checkLike(
			@Parameter(description = "名片ID") @PathVariable Long cardId) {

		boolean isLiked = businessCardFavoriteService.isCardLiked(cardId);
		return R.data(isLiked);
	}

	/**
	 * 检查名片收藏状态
	 */
	@GetMapping("/check-favorite/{cardId}")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "检查收藏状态", description = "检查用户是否已收藏名片")
	public R<Boolean> checkFavorite(
			@Parameter(description = "名片ID") @PathVariable Long cardId) {

		boolean isFavorited = businessCardFavoriteService.isCardFavorited(cardId);
		return R.data(isFavorited);
	}
}
